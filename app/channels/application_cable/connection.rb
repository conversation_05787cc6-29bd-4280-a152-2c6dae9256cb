module ApplicationCable
  class Connection < ActionCable::Connection::Base
    identified_by :current_user

    def connect
      self.current_user = find_verified_user
      # Note: Online status is now managed globally, not by ActionCable connections
      if current_user
        Rails.logger.info "ActionCable: User #{current_user.id} connected (global status mode)"
      end
    end

    def disconnect
      # Note: Online status is managed globally, not by individual ActionCable disconnections
      if current_user
        Rails.logger.info "ActionCable: User #{current_user.id} disconnected (global status mode)"
      end
    end

    private

    def find_verified_user

      # Try to get user ID from a custom cookie or session
      user = find_user_from_custom_approach

      if user
        return user
      end

      # For development, allow connections with a fallback user
      if Rails.env.development?
        user = User.first
        if user
          return user
        end
      end

      reject_unauthorized_connection
    end

    def find_user_from_custom_approach
      # Method 1: Try to get user ID from a simple cookie
      if cookies['current_user_id']
        user_id = cookies['current_user_id'].to_i
        user = User.find_by(id: user_id)
        if user
          if user_has_valid_session?(user)
            return user
          else
            Rails.logger.warn "ActionCable: User #{user.id} found but no valid session"
          end
        else
          Rails.logger.warn "ActionCable: User ID #{user_id} from cookie not found in database"
        end
      end

      nil
    rescue => e
      Rails.logger.warn "ActionCable: Custom authentication failed: #{e.message}"
      nil
    end

    def user_has_valid_session?(user)
      # Simple check - if user was marked online recently, they likely have a valid session
      status = UserOnlineStatus.find_by(user_id: user.id)
      return true if status && status.last_seen_at > 10.minutes.ago

      true
    end
  end
end
