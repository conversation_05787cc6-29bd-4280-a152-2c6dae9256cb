class ChatChannel < ApplicationCable::Channel
  def subscribed
    return reject unless current_user && params[:recipient_id]

    # Create a consistent channel name regardless of who initiates
    # Use the smaller user ID first to ensure consistency
    user_ids = [current_user.id.to_i, params[:recipient_id].to_i].sort
    channel_name = "chat_#{user_ids[0]}_#{user_ids[1]}"

    stream_from channel_name

    # Mark any unread messages from this recipient as read
    mark_messages_as_read
  end

  def unsubscribed
    stop_all_streams
  end

  # Handle sending messages through ActionCable
  def speak(data)
    return unless current_user && params[:recipient_id] && data['message'].present?

    # Create a message in the database with proper connection handling
    message = nil
    begin
      # Ensure we have a proper database connection for partitioned tables
      ActiveRecord::Base.connection_pool.with_connection do |connection|
        # Use GuardRail to ensure we're using the primary database for writes
        GuardRail.activate(:primary) do
          message = Message.create!(
            user_id: current_user.id,
            recipient_id: params[:recipient_id],
            body: data['message'],
            message_type: 'chat',
            delivered_at: Time.current
          )
        end
      end

      # Broadcast to both users' channels
      broadcast_message(message) if message
    rescue => e
      Rails.logger.error "ChatChannel: Message creation failed - #{e.class}: #{e.message}"
      Rails.logger.error "ChatChannel: Backtrace: #{e.backtrace.first(10).join(', ')}"

      # Send error message back to the client
      transmit({
        type: 'error',
        message: 'Failed to send message. Please try again.',
        error_details: Rails.env.development? ? e.message : nil
      })
    end
  end

  # Handle marking messages as read
  def mark_as_read(data)
    return unless current_user && data['sender_id']

    messages_marked = 0
    begin
      # Mark messages as read with proper connection handling
      ActiveRecord::Base.connection_pool.with_connection do |connection|
        GuardRail.activate(:primary) do
          messages_marked = Message.where(
            user_id: data['sender_id'],
            recipient_id: current_user.id,
            message_type: 'chat',
            seen_at: nil
          ).update_all(seen_at: Time.current)
        end
      end

      # Use consistent channel name for read receipt
      user_ids = [current_user.id.to_i, data['sender_id'].to_i].sort
      channel_name = "chat_#{user_ids[0]}_#{user_ids[1]}"

      # Broadcast read receipt
      ActionCable.server.broadcast(
        channel_name,
        {
          type: 'messages_read',
          reader_id: current_user.id,
          sender_id: data['sender_id'],
          messages_marked: messages_marked
        }
      )
      Rails.logger.info "ChatChannel: Successfully broadcast read receipt to #{channel_name}"
    rescue => e
      Rails.logger.error "ChatChannel: Read receipt broadcast failed - #{e.class}: #{e.message}"
      Rails.logger.error "ChatChannel: Backtrace: #{e.backtrace.first(10).join(', ')}"
    end
  end

  private

  def broadcast_message(message)
    message_data = {
      type: 'new_message',
      message: message.as_json(include: { user: { only: [:id, :name] } })
    }

    # Use the same consistent channel name as in subscribed
    user_ids = [current_user.id.to_i, params[:recipient_id].to_i].sort
    channel_name = "chat_#{user_ids[0]}_#{user_ids[1]}"

    begin
      # Broadcast to the single consistent channel
      ActionCable.server.broadcast(channel_name, message_data)
      Rails.logger.info "ChatChannel: Successfully broadcast message to #{channel_name}"

      # Also broadcast a global notification to the recipient
      # This ensures they get notified even if they don't have this chat open
      ActionCable.server.broadcast(
        "global_notifications_#{params[:recipient_id]}",
        {
          type: 'new_message_notification',
          sender_id: current_user.id,
          sender_name: current_user.name,
          message_preview: message.body.truncate(50),
          message_count: 1
        }
      )
      Rails.logger.info "ChatChannel: Successfully broadcast notification to global_notifications_#{params[:recipient_id]}"
    rescue => e
      Rails.logger.error "ChatChannel: Broadcast failed - #{e.class}: #{e.message}"
      Rails.logger.error "ChatChannel: Backtrace: #{e.backtrace.first(5).join(', ')}"

      # Optionally, you could store the message for retry or notify the user
      # For now, we'll just log the error and continue
    end
  end

  def mark_messages_as_read
    return unless current_user && params[:recipient_id]

    begin
      ActiveRecord::Base.connection_pool.with_connection do |connection|
        GuardRail.activate(:primary) do
          Message.where(
            user_id: params[:recipient_id],
            recipient_id: current_user.id,
            message_type: 'chat',
            seen_at: nil
          ).update_all(seen_at: Time.current)
        end
      end
    rescue => e
      Rails.logger.error "ChatChannel: Auto mark as read failed - #{e.class}: #{e.message}"
    end
  end
end
