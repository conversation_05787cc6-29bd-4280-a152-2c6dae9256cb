class GlobalNotificationChannel < ApplicationCable::Channel
  def subscribed
    return reject unless current_user && params[:user_id]
    
    # Make sure the user can only subscribe to their own notifications
    return reject unless current_user.id.to_s == params[:user_id].to_s
    
    # Stream from a user-specific notification channel
    stream_from "global_notifications_#{current_user.id}"
    
    Rails.logger.info "GlobalNotificationChannel: User #{current_user.id} subscribed to global notifications"
  end

  def unsubscribed
    stop_all_streams
    Rails.logger.info "GlobalNotificationChannel: User #{current_user.id} unsubscribed from global notifications"
  end
end
