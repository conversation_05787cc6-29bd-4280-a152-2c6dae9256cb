class PresenceChannel < ApplicationCable::Channel
  def subscribed
    stream_from "presence_channel"
    Rails.logger.info "PresenceChannel: User #{current_user&.id} subscribed to presence channel (global status mode)"

    # Note: Online status is now managed globally, not by individual channel subscriptions
    # This channel is only used for broadcasting status updates to all clients
  end

  def unsubscribed
    Rails.logger.info "PresenceChannel: User #{current_user&.id} unsubscribed from presence channel (global status mode)"

    # Note: Online status is managed globally, not by individual channel subscriptions
    # Users remain online until they logout or become inactive globally
  end

  private

  def update_status(status)
    return unless current_user

    Rails.logger.info "PresenceChannel: Updating status for user #{current_user.id} to #{status ? 'online' : 'offline'}"

    # Use database to track online status (consistent with the rest of the app)
    UserOnlineStatus.update_status(current_user.id, online: status)

    begin
      # Broadcast status change to all connected clients
      ActionCable.server.broadcast("presence_channel", {
        user_id: current_user.id,
        online: status,
        timestamp: Time.current.iso8601
      })
      Rails.logger.info "PresenceChannel: Status update and broadcast complete for user #{current_user.id}"
    rescue => e
      Rails.logger.error "PresenceChannel: Broadcast failed for user #{current_user.id} - #{e.class}: #{e.message}"
      Rails.logger.error "PresenceChannel: Backtrace: #{e.backtrace.first(5).join(', ')}"
    end
  end
end
