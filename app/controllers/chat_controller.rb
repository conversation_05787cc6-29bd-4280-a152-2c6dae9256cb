class ChatController < ApplicationController
  before_action :require_user

  def index
    # Set up any initial data needed for the chat page
    @current_user_id = @current_user.id
    @current_user_name = @current_user.name
    @current_user_avatar = @current_user.avatar_image_url || '/images/messages/avatar-50.png'

    # Set a simple cookie for ActionCable authentication
    cookies['current_user_id'] = {
      value: @current_user.id.to_s,
      httponly: false,  # Allow JavaScript access
      secure: false,    # For development
      expires: 1.day.from_now
    }

    # Set page title and breadcrumbs
    add_crumb t('Chat'), chat_path
    set_active_tab "chat"
  end

  # Temporary debugging endpoint to check authentication
  def debug_auth
    render json: {
      current_user: @current_user&.id,
      session_keys: session.keys,
      pseudonym_session: PseudonymSession.find&.record&.user&.id,
      cookies: cookies.signed["_normandy_session"]&.keys
    }
  end
end
