class UnifiedMessagesController < ApplicationController
  before_action :require_user
  before_action :reject_student_view_student

  def index
    # Set up data for both inbox and chat functionality
    @current_user_id = @current_user.id
    @current_user_name = @current_user.name
    @current_user_avatar = @current_user.avatar_image_url || '/images/messages/avatar-50.png'

    # Set a simple cookie for ActionCable authentication (for chat)
    cookies['current_user_id'] = {
      value: @current_user.id.to_s,
      httponly: false,  # Allow JavaScript access
      secure: false,    # For development
      expires: 1.day.from_now
    }

    # Set page title and breadcrumbs
    @page_title = t('Messages')
    add_crumb t('Messages'), messages_path
    set_active_tab "messages"

    # Get unread counts for tab badges
    @unread_conversations_count = @current_user.conversations.unread.size
    @unread_chat_count = Message.where(
      recipient_id: @current_user.id,
      message_type: 'chat',
      seen_at: nil
    ).count

    # Determine which tab to show initially (default to inbox)
    @active_tab = params[:tab] || 'inbox'

    # Set environment variable to indicate unified messages context
    js_env :UNIFIED_MESSAGES_PAGE => true

    # Add CSS and JS bundles for both inbox and chat
    css_bundle :canvas_inbox
    js_bundle :inbox
    js_bundle :chat_page

    respond_to do |format|
      format.html { render layout: true }
      format.json { redirect_to api_v1_conversations_url }
    end
  end
end
