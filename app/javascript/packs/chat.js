import consumer from "../channels/consumer"

window.initializeChat = function() {
  const currentUserId = ENV.current_user_id;
  let currentRecipientId = null;
  let chatSubscription = null;
  let presenceSubscription = null;
  const chatButton = document.getElementById('chat-button');
  const chatModal = document.getElementById('chat-modal');
  const closeChat = document.querySelector('.close-chat');
  const chatForm = document.getElementById('chat-form');
  const chatInput = document.getElementById('chat-input');
  const sendButton = document.getElementById('send-button');
  const recipientIdInput = document.getElementById('recipient-id');
  const chatMessages = document.getElementById('chat-messages');
  const selectedUserName = document.getElementById('selected-user-name');
  const unreadMessagesBadge = document.getElementById('unread-messages-badge');
  
  // Keep track of unread messages per user
  const unreadMessages = {};
  let totalUnreadCount = 0;
  let globalUnreadCounts = {}; // Store unread counts from server
  
  // Initialize the chat
  function initialize() {
    setupEventListeners();
    subscribeToPresenceChannel();

    // Initial update of user status, user list, and unread counts
    updateUserStatus();

    // Update online status, user list, and unread counts every 10 seconds
    setInterval(updateUserStatus, 10000);
  }
  

  
  // Store all users for search functionality
  let allUsers = [];

  // Render the users list in the sidebar
  function renderUsersList(users, searchTerm = '') {

    allUsers = users; // Store for search
    const usersList = document.querySelector('.chat-users-list');
    usersList.innerHTML = '';

    // Filter users based on search term
    const filteredUsers = users.filter(user =>
      user.id !== currentUserId &&
      user.name.toLowerCase().includes(searchTerm.toLowerCase())
    );

    filteredUsers.forEach(user => {
      const userDiv = document.createElement('div');
      userDiv.className = 'chat-user-item';
      userDiv.dataset.userId = user.id;

      const avatarUrl = user.avatar_image_url || '/images/messages/avatar-50.png';
      const statusText = user.online ? 'Online' : 'Offline';

      userDiv.innerHTML = `
        <img src="${avatarUrl}" alt="${user.name}" class="user-avatar" onerror="this.src='/images/messages/avatar-50.png'">
        <div class="user-info">
          <div class="user-name">${user.name}</div>
          <div class="user-status-indicator">
            <div class="user-status ${user.online ? 'online' : 'offline'}"></div>
            <span class="user-status-text">${statusText}</span>
          </div>
        </div>
        <span class="user-unread-badge" style="display: none;">
          <span class="unread-count-number">0</span>
        </span>
      `;

      userDiv.addEventListener('click', () => {
        selectUser(user);
      });

      usersList.appendChild(userDiv);
    });

    // Show "No users found" message if search returns no results
    if (filteredUsers.length === 0 && searchTerm) {
      const noResultsDiv = document.createElement('div');
      noResultsDiv.className = 'no-users-found';
      noResultsDiv.innerHTML = `
        <div style="text-align: center; padding: 20px; color: #666;">
          <i class="icon-solid icon-search" style="font-size: 24px; margin-bottom: 8px; opacity: 0.5;"></i>
          <p>No users found</p>
        </div>
      `;
      usersList.appendChild(noResultsDiv);
    }
  }



  // Function to update user notifications
  function updateUserNotifications() {
    const usersList = document.querySelector('.chat-users-list');
    if (!usersList) {
      console.log('User list not found, skipping notification update');
      return;
    }

    const userItems = usersList.querySelectorAll('.chat-user-item');
    if (userItems.length === 0) {
      console.log('No user items found, skipping notification update');
      return;
    }

    console.log('Updating notifications with counts:', globalUnreadCounts);

    userItems.forEach(userItem => {
      const userId = userItem.dataset.userId;
      const unreadCount = globalUnreadCounts[userId] || 0;

      // Remove existing notification indicators
      const existingIndicator = userItem.querySelector('.unread-indicator');
      if (existingIndicator) {
        existingIndicator.remove();
      }

      // Remove existing background highlighting
      userItem.classList.remove('has-unread-messages');
      userItem.style.backgroundColor = '';

      // Update notification badge if there are unread messages and this user is not currently selected
      const badge = userItem.querySelector('.user-unread-badge');
      const countElement = badge?.querySelector('.unread-count-number');

      if (unreadCount > 0 && currentRecipientId != userId) {
        console.log(`Adding notification for user ${userId} with ${unreadCount} unread messages`);

        // Update badge with unread count
        if (badge && countElement) {
          countElement.textContent = unreadCount;
          badge.style.display = 'block';
        }

        // Add green background highlighting
        userItem.classList.add('has-unread-messages');
        userItem.style.backgroundColor = '#d4edda';
      } else if (badge) {
        // Hide badge when no unread messages
        badge.style.display = 'none';
      }
    });
  }

  // Set up event listeners
  function setupEventListeners() {
    // Toggle chat modal
    chatButton.addEventListener('click', () => {
      chatModal.style.display = chatModal.style.display === 'none' ? 'block' : 'none';
    });

    // Search functionality
    const searchInput = document.getElementById('chat-search');
    if (searchInput) {
      searchInput.addEventListener('input', (e) => {
        const searchTerm = e.target.value;
        renderUsersList(allUsers, searchTerm);
      });
    }

    // Close chat modal
    closeChat.addEventListener('click', () => {
      chatModal.style.display = 'none';
    });
    
    // Handle form submission
    chatForm.addEventListener('submit', (e) => {
      e.preventDefault();
      sendMessage();
    });
  }
  
  // Select a user to chat with
  function selectUser(user) {
    // Clear previous active state
    const previousActive = document.querySelector('.chat-user-item.active');
    if (previousActive) {
      previousActive.classList.remove('active');
    }

    // Set active state
    const userDiv = document.querySelector(`.chat-user-item[data-user-id="${user.id}"]`);
    if (userDiv) {
      userDiv.classList.add('active');
    }

    // Update selected user info
    selectedUserName.textContent = user.name;
    const selectedUserStatus = document.getElementById('selected-user-status');
    const selectedUserAvatar = document.getElementById('selected-user-avatar');

    if (selectedUserStatus) {
      selectedUserStatus.textContent = user.online ? 'Online' : 'Offline';
    }

    if (selectedUserAvatar) {
      const avatarUrl = user.avatar_image_url || '/images/messages/avatar-50.png';
      selectedUserAvatar.src = avatarUrl;
      selectedUserAvatar.style.display = 'block';
      selectedUserAvatar.onerror = function() { this.src = '/images/messages/avatar-50.png'; };
    }

    // Hide chat placeholder
    const chatPlaceholder = document.querySelector('.chat-placeholder');
    if (chatPlaceholder) {
      chatPlaceholder.style.display = 'none';
    }

    currentRecipientId = user.id;
    recipientIdInput.value = user.id;

    // Enable input and button
    chatInput.disabled = false;
    sendButton.disabled = false;
    
    // Subscribe to chat channel for this user
    subscribeToChannelForUser(user.id);
    
    // Load previous messages
    loadMessages(user.id);
    
    // Mark messages as read
    if (unreadMessages[user.id]) {
      totalUnreadCount -= unreadMessages[user.id];
      unreadMessages[user.id] = 0;
      updateUnreadBadge();
      markMessagesAsRead(user.id);
    }

    // Clear unread notifications for this user
    if (globalUnreadCounts[user.id]) {
      globalUnreadCounts[user.id] = 0;
      markMessagesAsRead(user.id);
    }

    // Update notifications to remove indicators for this user
    updateUserNotifications();

    // Hide unread badge for this user
    const badge = userDiv.querySelector('.user-unread-badge');
    if (badge) {
      badge.style.display = 'none';
    }
  }
  
  // Update user online status, user list, and unread counts
  function updateUserStatus() {
    fetch('/api/v1/chat/update_status', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
      },
      body: JSON.stringify({ online: true })
    })
    .then(response => response.json())
    .then(data => {
      console.log('Status update response:', data);

      // Update user list with the new data
      if (data.users) {
        renderUsersList(data.users);
      }

      // Update global unread counts from the response
      if (data.unread_counts) {
        globalUnreadCounts = data.unread_counts;

        // Update user list with notification indicators
        setTimeout(() => {
          updateUserNotifications();
        }, 50);
      }
    })
    .catch(error => console.error('Error updating status:', error));
  }
  
  // Subscribe to presence channel
  function subscribeToPresenceChannel() {
    presenceSubscription = consumer.subscriptions.create("PresenceChannel", {
      connected() {
        console.log("Connected to presence channel");
      },
      disconnected() {
        console.log("Disconnected from presence channel");
      },
      received(data) {
        if (data.user_id && data.online !== undefined) {
          updateUserStatusIndicator(data.user_id, data.online);
        }
      }
    });
  }
  
  // Update user status indicator in UI
  function updateUserStatusIndicator(userId, isOnline) {
    const userDiv = document.querySelector(`.chat-user-item[data-user-id="${userId}"]`);
    if (userDiv) {
      const statusIndicator = userDiv.querySelector('.user-status');
      const statusText = userDiv.querySelector('.user-status-text');

      if (statusIndicator) {
        statusIndicator.className = `user-status ${isOnline ? 'online' : 'offline'}`;
      }

      if (statusText) {
        statusText.textContent = isOnline ? 'Online' : 'Offline';
      }
    }

    // Update selected user status if this is the currently selected user
    if (currentRecipientId && parseInt(userId) === parseInt(currentRecipientId)) {
      const selectedUserStatus = document.getElementById('selected-user-status');
      if (selectedUserStatus) {
        selectedUserStatus.textContent = isOnline ? 'Online' : 'Offline';
      }
    }
  }
  
  // Subscribe to chat channel for a specific user
  function subscribeToChannelForUser(userId) {
    // Unsubscribe from previous channel if any
    if (chatSubscription) {
      chatSubscription.unsubscribe();
    }
    
    chatSubscription = consumer.subscriptions.create(
      { channel: "ChatChannel", recipient_id: userId },
      {
        connected() {
          console.log(`Connected to ChatChannel for recipient ${userId}`);
        },
        disconnected() {
          console.log(`Disconnected from ChatChannel for recipient ${userId}`);
        },
        received(data) {
          if (data.message) {
            const isCurrentChat = currentRecipientId === data.message.user_id;
            
            if (isCurrentChat) {
              appendMessage(data.message);
              markMessagesAsRead(data.message.user_id);
            } else {
              // Increment unread count
              unreadMessages[data.message.user_id] = (unreadMessages[data.message.user_id] || 0) + 1;
              totalUnreadCount++;
              updateUnreadBadge();
              
              // Update user badge
              const userDiv = document.querySelector(`.chat-user-item[data-user-id="${data.message.user_id}"]`);
              if (userDiv) {
                const badge = userDiv.querySelector('.user-unread-badge');
                const countElement = badge?.querySelector('.unread-count-number');
                if (badge && countElement) {
                  countElement.textContent = unreadMessages[data.message.user_id];
                  badge.style.display = 'block';
                }
              }
            }
          }
        }
      }
    );
  }
  
  // Load previous messages for a user
  function loadMessages(userId) {
    chatMessages.innerHTML = '<div class="loading-messages">Loading messages...</div>';
    
    fetch(`/api/v1/chat/messages?recipient_id=${userId}`)
      .then(response => response.json())
      .then(data => {
        chatMessages.innerHTML = '';
        if (data.messages.length === 0) {
          chatMessages.innerHTML = '<div class="no-messages">No messages yet. Start a conversation!</div>';
        } else {
          data.messages.forEach(message => {
            appendMessage(message);
          });
          // Scroll to bottom
          chatMessages.scrollTop = chatMessages.scrollHeight;
        }
      })
      .catch(error => {
        console.error('Error loading messages:', error);
        chatMessages.innerHTML = '<div class="error-message">Failed to load messages. Please try again.</div>';
      });
  }
  
  // Send a message
  function sendMessage() {
    const message = chatInput.value.trim();
    if (!message || !currentRecipientId) return;
    
    fetch('/api/v1/chat/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
      },
      body: JSON.stringify({
        message: {
          body: message,
          recipient_id: currentRecipientId
        }
      })
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        // Clear input
        chatInput.value = '';
        
        // Focus back on input
        chatInput.focus();
      }
    })
    .catch(error => console.error('Error sending message:', error));
  }
  
  // Append a message to the chat
  function appendMessage(message) {
    const messageDiv = document.createElement('div');
    const isSent = message.user_id === currentUserId;
    
    messageDiv.className = `message ${isSent ? 'sent' : 'received'}`;
    
    const timestamp = new Date(message.created_at).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    
    messageDiv.innerHTML = `
      <div class="message-content">${message.body}</div>
      <div class="message-time">${timestamp}</div>
    `;
    
    chatMessages.appendChild(messageDiv);
    
    // Scroll to bottom
    chatMessages.scrollTop = chatMessages.scrollHeight;
  }
  
  // Mark messages as read
  function markMessagesAsRead(senderId) {
    fetch('/api/v1/chat/mark_as_read', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
      },
      body: JSON.stringify({
        sender_id: senderId
      })
    }).catch(error => console.error('Error marking messages as read:', error));
  }
  
  // Update unread badge count
  function updateUnreadBadge() {
    if (totalUnreadCount > 0) {
      unreadMessagesBadge.textContent = totalUnreadCount > 9 ? '9+' : totalUnreadCount;
      unreadMessagesBadge.style.display = 'block';
    } else {
      unreadMessagesBadge.style.display = 'none';
    }
  }
  
  // Initialize when document is ready
  initialize();
};
