class UserOnlineStatus < ActiveRecord::Base
  belongs_to :user
  
  validates :user_id, presence: true, uniqueness: true
  validates :last_seen_at, presence: true
  validates :online, inclusion: { in: [true, false] }
  
  # Consider a user online if they were seen within the last 5 minutes
  ONLINE_THRESHOLD = 5.minutes
  
  # Update or create online status for a user
  def self.update_status(user_id, online: true)
    status = find_or_initialize_by(user_id: user_id)
    old_status = status.online
    status.last_seen_at = Time.current
    status.online = online

    if old_status != online
      Rails.logger.info "UserOnlineStatus: User #{user_id} status changed from #{old_status ? 'online' : 'offline'} to #{online ? 'online' : 'offline'}"
    end

    status.save!
    status
  end
  
  # Check if a user is online based on their last seen time
  def self.online?(user_id)
    status = find_by(user_id: user_id)
    return false unless status

    # User is online if they explicitly set online status and were seen recently
    status.online && status.last_seen_at > ONLINE_THRESHOLD.ago
  end

  # Get all online users
  def self.online_users
    where(online: true)
      .where('last_seen_at > ?', ONLINE_THRESHOLD.ago)
      .preload(:user)
  end
  
  # Mark user as offline
  def self.mark_offline(user_id)
    status = find_by(user_id: user_id)
    if status
      Rails.logger.info "UserOnlineStatus: Marking user #{user_id} as offline (was #{status.online ? 'online' : 'offline'})"
      status.update!(online: false)
    else
      Rails.logger.info "UserOnlineStatus: No status record found for user #{user_id} when trying to mark offline"
    end
  end
  
  # Clean up old offline statuses (optional maintenance task)
  def self.cleanup_old_statuses
    where('last_seen_at < ?', 1.day.ago).delete_all
  end

  # Clean up stale online statuses - mark users offline if they haven't been seen recently
  def self.cleanup_stale_statuses
    stale_threshold = ONLINE_THRESHOLD.ago
    stale_users = where(online: true).where('last_seen_at < ?', stale_threshold)

    stale_count = stale_users.count
    if stale_count > 0
      
      # Get user IDs before updating
      stale_user_ids = stale_users.pluck(:user_id)

      # Mark them offline
      stale_users.update_all(online: false)

      # Broadcast offline status for each user
      stale_user_ids.each do |user_id|
        ActionCable.server.broadcast("presence_channel", {
          user_id: user_id,
          online: false,
          timestamp: Time.current.iso8601,
          reason: 'stale_cleanup'
        })
      end
    end
  end
  
  # Instance method to check if this status represents an online user
  def online?
    online && last_seen_at > ONLINE_THRESHOLD.ago
  end
end
