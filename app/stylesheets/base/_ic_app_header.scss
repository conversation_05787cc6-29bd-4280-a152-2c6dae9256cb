/*
 * Copyright (C) 2015 - present Instructure, Inc.
 *
 * This file is part of Canvas.
 *
 * Canvas is free software: you can redistribute it and/or modify it under
 * the terms of the GNU Affero General Public License as published by the Free
 * Software Foundation, version 3 of the License.
 *
 * Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
 * WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
 * A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
 * details.
 *
 * You should have received a copy of the GNU Affero General Public License along
 * with this program. If not, see <http://www.gnu.org/licenses/>.
 */

// @mixin primary-nav-badge-border($border-color) { box-shadow: 0 0 0 2px $border-color; }

/// These are editable variables from the theme editor.
/// For more info view stylesheets/brandable_variables.json
// ic-brand-global-nav-bgd
// ic-brand-global-nav-ic-icon-svg-fill
// ic-brand-global-nav-menu-item__text-color
// ic-brand-global-nav-avatar-border
// ic-brand-global-nav-menu-item__badge-bgd
// ic-brand-global-nav-menu-item__badge-text
// ic-brand-global-nav-ic-icon-svg-fill--active
// ic-brand-global-nav-logo-bgd

$ic-global-nav-link-hover: rgba(black, 0.2);
$ic-global-nav-link-active: rgba(black, 0.4);
$ic-masquerade-color: $ic-color-action;
$ic-subnav-panel-bgd: $ic-color-light;
$ic-subnav-tray-bgd: black;
$ic-tooltip-arrow-size: 0.375rem;

// ==============================================
// RSU CUSTOM HEADER STYLES
// ==============================================
.rsu-header {
  background: linear-gradient(to bottom, #f4a945 0%, #f4a945 8px, #225745 8px, #225745 100%);
  padding: 0;
  margin: 0;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.rsu-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 20px;
  max-width: 1800px;
  margin: 0 auto;
}

.rsu-logo {
  width: 60px;
  height: 60px;
  margin-right: 15px;
  flex-shrink: 0;
}

.rsu-logo-link {
  display: block;
  width: 100%;
  height: 100%;
  text-decoration: none;
  transition: transform 0.2s ease;

  &:hover,
  &:focus {
    transform: scale(1.05);
    outline: none;
  }

  &:focus {
    box-shadow: 0 0 0 2px #ffffff, 0 0 0 4px #2d5f3f;
  }
}

.rsu-logo img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.rsu-text {
  color: white;
  font-family: Arial, sans-serif;
  flex-grow: 1;
}

.rsu-university-name {
  font-size: 16px;
  margin: 0;
  line-height: 1.2;
}

.rsu-system-name {
  font-size: 24px;
  font-weight: bold;
  margin: 0;
  line-height: 1.2;
  opacity: 0.9;
}

// Sign-out button styles
.rsu-actions {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.rsu-signout-btn {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  color: white;
  text-decoration: none;
  font-family: Arial, sans-serif;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;

  &:hover,
  &:focus {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    text-decoration: none;
    color: white;
    outline: none;
  }

  &:focus {
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.5);
  }

  &:active {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(1px);
  }
}

.rsu-signout-text {
  font-size: 12px;
  white-space: nowrap;
}

// Responsive adjustments
@media (max-width: 768px) {
  .rsu-header-content {
    padding: 15px 15px;
  }
  
  .rsu-university-name {
    font-size: 14px;
  }
  
  .rsu-system-name {
    font-size: 20px;
  }
  
  .rsu-signout-btn {
    padding: 6px 10px;
  }
}

@media (max-width: 480px) {
  .rsu-header-content {
    padding: 10px 10px;
  }
  
  .rsu-logo {
    width: 50px;
    height: 50px;
    margin-right: 10px;
  }
  
  .rsu-university-name {
    font-size: 13px;
  }
  
  .rsu-system-name {
    font-size: 18px;
  }
  
  .rsu-signout-btn {
    padding: 5px 8px;
  }
}

#application {
  position: relative;
}

@include desktop-only {
  .tray-with-space-for-global-nav {
    margin-#{direction(left)}: $ic-header-primary-width - 30;
    body.primary-nav-expanded & {
      margin-#{direction(left)}: $ic-header-primary-width ;
    }
  }
  .navigation-tray-container {
    min-height: 100vh;
  }
}

.ic-app-header {
  box-sizing: border-box;
  position: fixed;
  top: 110px;
  #{direction(left)}: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
  width: $ic-header-primary-width - 30;
  z-index: 100;
  background-color: var(--ic-brand-global-nav-bgd);
}

.ic-app-header__main-navigation {
  box-sizing: border-box;
  flex: 1 0 auto;
}

.ic-app-header__main-navigation a {
  text-decoration: inherit;
}

.ic-app-header__secondary-navigation {
  box-sizing: border-box;
  position: absolute;
  #{direction(left)}: 0;
  bottom: 0;
  z-index: 1;
  width: 100%;
}

.ic-app-header__menu-list {
  @include reset-list;
}

.ic-app-header__menu-list-item {
  box-sizing: border-box;

  svg {
    // added to normalize size of icons between old and new global nav
    width: 1.625rem;
    height: 1.625rem;
  }
  &.ic-app-header__menu-list-item--active {
    .ic-app-header__menu-list-link {
      background: transparent;
      color: #f4b952;

      body:not(.primary-nav-expanded) & {
        &:hover,
        &:focus {
          .menu-item__text {
            transition: none;
            transition-delay: 0;
            transform: none;
            opacity: 0;
          }
        }
      }
      &:focus {
        box-shadow: inset 0 0 0 1px var(--ic-brand-global-nav-bgd),
          inset 0 0 0 2px #f4b952;
      }
    }
    .ic-icon-svg {
      fill: #f4b952;
    }
    .menu-item__badge {
      background: var(--ic-brand-global-nav-menu-item__badge-bgd--active);
      color: var(--ic-brand-global-nav-menu-item__badge-text--active);
    }
    .ic-avatar {
      border-color: $ic-border-light;
      &.ic-avatar--fake-student {
        border-color: $ic-masquerade-color;
      }
    }
    .menu-item-icon-container,
    .menu-item-icon-container svg,
    .menu-item-icon-container i,
    .menu-item-icon-container [class*="icon-"] {
      color: #f4b952;
      fill: #f4b952;
    }
  }
}

.ic-app-header__menu-list-link {
  box-sizing: border-box;
  transition: background-color 0.3s, padding 0.3s;
  position: relative;
  text-align: center;
  display: block;
  color: var(--ic-brand-global-nav-menu-item__text-color);
  border-style: none;

  &:not(.ic-app-header__menu-list-link--nav-toggle) {
    padding: 0.25rem 0;
    @media only screen and (min-height: 400px) {
      padding: 0.4375rem 0;
    }
  }

  &.ic-app-header__menu-list-link--nav-toggle {
    width: 100%;
    perspective: 600px;

    .ic-icon-svg--navtoggle {
      transition: all 0.4s;
      transform: direction-if(rtl, rotate3d(0, 1, 0, -180deg));
    }
  }

  &:hover,
  &:focus {
    background-color: $ic-global-nav-link-hover;
    @if $use_high_contrast ==false {
      text-decoration: none;
      outline: none;
    }
    .ic-avatar {
      border-color: var(--ic-brand-global-nav-avatar-border);
      &.ic-avatar--fake-student {
        border-color: $ic-masquerade-color;
      }
    }
    .menu-item__text {
      transform: translate3d($ic-tooltip-arrow-size, 0, 0);
      opacity: 1;
    }
  }

  &:focus {
    box-shadow: inset 0 0 0 1px var(--ic-brand-global-nav-bgd),
      inset 0 0 0 2px var(--ic-brand-global-nav-menu-item__text-color);
  }

  &:active {
    background-color: $ic-global-nav-link-active;
    box-shadow: inset 0 1px 1px rgba(black, 0.3);
  }

  .ic-icon-svg {
    width: 26px;
    display: inline-block;
    vertical-align: middle;
    fill: var(--ic-brand-global-nav-ic-icon-svg-fill);
  }
}

button.ic-app-header__menu-list-link {
  background: transparent;
  outline: none;
  width: 100%;
}

.ic-avatar {
  overflow: hidden;
  border-radius: 100%;
  border: 2px solid var(--ic-brand-global-nav-avatar-border);
  width: 50px;
  height: 50px;
  box-sizing: border-box;
  display: inline-block;
  vertical-align: middle;
  &.ic-avatar--fake-student {
    border-color: $ic-masquerade-color;
  }
}

.menu-item-icon-container {
  transform: translate3d(0, 0, 0);
  position: relative;
  z-index: 1;
  width: $ic-sp * 4;
  margin: 0 auto;
  color: var(--ic-brand-global-nav-ic-icon-svg-fill);

  .ic-avatar {
    transform: translate3d(0, 0, 0);
    width: $ic-sp * 2.7;
    height: $ic-sp * 2.7;

    body.primary-nav-expanded & {
      width: $ic-sp * 3;
      height: $ic-sp * 3;
    }
  }
}

.menu-item__text {
  // set up properties to transition on hover/focus
  transform: translate3d(-100%, 0, 0);
  opacity: 0;

  pointer-events: none;
  box-sizing: border-box;
  background-color: var(--ic-brand-global-nav-ic-icon-svg-fill--active);
  position: absolute;
  #{direction(left)}: 100%;
  top: 50%;
  padding: 0 $ic-sp;
  line-height: 1.8rem;
  margin-top: -0.9rem;
  @include fontSize($ic-font-size--xsmall);
  border-radius: $ic-border-radius * 0.5;
  color: $ic-color-light;
  white-space: nowrap;

  // this class is added after a 1s delay because the
  // transitions look weird when the nav is being closed
  body.primary-nav-transitions & {
    transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275), opacity 0.3s;
    transition-delay: 0.3s;
  }

  &::after {
    #{direction(right)}: 100%;
    top: 50%;
    border: solid transparent;
    content: '';
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
    border-color: transparent;
    border-#{direction(right)}-color: var(--ic-brand-global-nav-ic-icon-svg-fill--active);
    border-width: $ic-tooltip-arrow-size;
    margin-top: -($ic-tooltip-arrow-size);
  }
}

.menu-item__badge {
  @include ic-badge-maker;
  transition: all 0.3s ease-out;
  position: absolute;
  top: -0.5em;
  #{direction(right)}: 0;
  background: var(--ic-brand-global-nav-menu-item__badge-bgd);
  color: var(--ic-brand-global-nav-menu-item__badge-text);
}

.ic-app-header__logomark-container {
  width: 100%;
  background-color: var(--ic-brand-global-nav-logo-bgd);
  box-sizing: border-box;
}

.ic-app-header__logomark {
  display: block;
  overflow: hidden;
  background-image: var(--ic-brand-header-image);
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: contain;
  transition: transform 0.3s;
  height: 75px;

  body.primary-nav-expanded & {
    height: 65px;
    margin-top: 15px;
    margin-bottom: 15px;
  }
  &:focus {
    outline-style: none;
    box-shadow: inset 0 0 0 1px var(--ic-brand-global-nav-bgd),
      inset 0 0 0 2px var(--ic-brand-global-nav-menu-item__text-color);
  }
}

// set to block to avoid any exta inline spacing at bottom
.ic-app-header__uploaded-logo {
  display: block;
}

.ic-app-header__menu-list-link {
  padding: 0.5625rem 0;
}

body.primary-nav-expanded {
  .ic-app-header {
    width: $ic-header-primary-width; 
    overflow-y: auto;
    -ms-overflow-style: none; // IE11 and below was throwing on scrollbars no matter if needed or not
  }

  .ic-app-header__secondary-navigation {
    position: static;
    #{direction(left)}: auto;
    bottom: auto;
    z-index: auto;
    width: auto;
  }

  .ic-app-header__menu-list-item.ic-app-header__menu-list-item--active {
    .menu-item__text {
      color: #f4b952;
    }
  }

  .ic-app-header__menu-list-link {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 0.5625rem 1rem;
    text-align: left;
    
    &.ic-app-header__menu-list-link--nav-toggle {
      .ic-icon-svg--navtoggle {
        transform: direction-if(rtl, none, rotate3d(0, 1, 0, -180deg));
      }
    }

    &:hover,
    &:focus {
      .menu-item__text {
        transform: none;
      }
    }

    &:not(.ic-app-header__menu-list-link--nav-toggle) {
      padding: 0.5625rem 1rem;
      @media only screen and (min-height: 400px) {
        padding: 0.5625rem 1rem;
      }
    }
  }

  .menu-item-icon-container {
    margin-right: 12px;
    margin-left: 0;
    flex-shrink: 0;
    width: auto;
    margin: 0 12px 0 0;
  }

  .menu-item__text {
    transition: none;
    position: static;
    #{direction(left)}: auto;
    top: auto;
    transform: none;
    opacity: 1;
    text-align: left;
    display: inline-block;
    line-height: 1.4;
    border-radius: 0;
    margin: 0;
    padding: 0;
    background: transparent;
    color: var(--ic-brand-global-nav-menu-item__text-color);
    white-space: nowrap;
    word-wrap: normal;
    flex-grow: 1;

    &::after {
      display: none;
    }
  }
}

// help dialog/tray styles
.ic-HelpDialog__form-legend {
  display: table;
  font-size: 1em;
  line-height: inherit;
  font-weight: bold;
  margin: 0;
  padding: 0;
  border: 0;
  min-width: 0;
}

.ic-HelpDialog__form-fieldset {
  border: 0;
  padding: 0.01em 0 0;
  min-width: 0;
  margin: direction-sides(0 0 $spacing-width*0.5 0);
}

.ic-HelpDialog__form-actions {
  display: flex;
  justify-content: flex-end;
}

#mobile-header {
  display: flex;
  align-items: center;
  z-index: 100;
  background-color: var(--ic-brand-global-nav-bgd);
  * {
    color: var(--ic-brand-global-nav-menu-item__text-color);
  }
}

#mobileContextNavContainer {
  overflow: hidden;
  // we transition max-height instead of height because otherwise we'd have to give it an explicit height and not "auto"
  transition: max-height 1.5s ease-out;
  max-height: 0px;
  &[aria-expanded='true'] {
    max-height: 3000px;
    margin-bottom: 50px;
    box-shadow: 0 0.375rem 0.4375rem rgba(0, 0, 0, 0.1), 0 0.625rem 1.75rem rgba(0, 0, 0, 0.25);
  }
}

@include desktop-only {
  #mobile-header,
  #mobileContextNavContainer {
    display: none;
  }
}

.mobile-header-title {
  text-align: center;
  display: block;
  flex-grow: 1;
}

.mobile-header-hamburger,
.mobile-header-student-view,
.mobile-header-arrow,
.mobile-header-title,
.mobile-header-space {
  padding: $ic-sp * 1.3;
}

.mobile-header-student-view {
  padding-#{direction('right')}: $ic-sp * 0.65;
}

.mobile-header-arrow {
  padding-#{direction('left')}: $ic-sp * 0.65;
}

.ic-brand-mobile-global-nav-logo {
  height: 48px;
  display: block;
  background-image: var(--ic-brand-mobile-global-nav-logo);
  background-position: top left;
  background-repeat: no-repeat;
  background-size: contain;
}

// Tourpoints Styles
#___reactour {
  .reactour__helper {
    background-color: white;
    color: unset;
    max-width: 515px;
    width: calc(100vw - 20px);
  }
  .tour-star-image {
    max-width: 120px;
    padding: 1rem 0;
    margin: 0 auto;
    text-align: center;
  }
}

.css-1gto5tw-tray {
  top: 110px !important;
}