/*
 * Copyright (C) 2015 - present Instructure, Inc.
 *
 * This file is part of Canvas.
 *
 * Canvas is free software: you can redistribute it and/or modify it under
 * the terms of the GNU Affero General Public License as published by the Free
 * Software Foundation, version 3 of the License.
 *
 * Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
 * WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
 * A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
 * details.
 *
 * You should have received a copy of the GNU Affero General Public License along
 * with this program. If not, see <http://www.gnu.org/licenses/>.
 */

@import "base/environment";

.report_description {
  display: none;
}

table.report_example, table.report {
  border-collapse: collapse;
  page-break-inside: avoid;
  thead, th {
    background-color: #ccc;
  }
  td, th {
    border: 1px solid #aaa;
    padding: 2px 5px;
  }
}

#tab-reports {
  .report_dialog {
    padding: 15px;
  }
  table.reports {
    margin: 5px 15px;
    width: 90%;
    border-collapse: collapse;
    tr.reports {
      border-bottom: 1px solid #DDD;
      th.reports {
        padding: 5px;
        background-color: #DDD;
        font-weight: bold;
        &.title {
          text-align: left;
          background-color: $ic-color-light;
        }
      }
      td.reports {
        padding: 5px;
        &.title {
          font-weight: bold;
        }
        &.action {
          text-align: center;
        }
      }
    }
  }
}

table.report.sortable {
  th.tablesorter-header {
    padding-#{direction(right)}: 20px;
    cursor: pointer;
    background: url(/images/tablesorter/bg.png) no-repeat right center;
    &:hover {
      background-image: url(/images/tablesorter/bg_hover.png);
    }
    &.tablesorter-headerAsc {
      background-image: url(/images/tablesorter/asc.gif);
    }
    &.tablesorter-headerDesc {
      background-image: url(/images/tablesorter/desc.gif);
    }
    &.sorter-false {
      background-image: none;
      cursor: default;
    }
  }
}

.report.sortable {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(44,62,80,0.07);
  margin-bottom: 2rem;
  overflow: hidden;
}
.report.sortable th, .report.sortable td {
  padding: 0.75em 1em;
  border: none;
}
.report.sortable thead th {
  background: #f4f6fa;
  color: #2c3e50;
  font-weight: 600;
  border-bottom: 2px solid #e1e4ea;
}
.report.sortable tbody tr {
  transition: background 0.2s;
}
.report.sortable tbody tr:hover {
  background: #f8fafc;
}
.access-summary {
  background: #f8f9fa;
  padding: 0.75rem 1rem;
  border-radius: 6px;
  margin-top: 0.5rem;
  border: 1px solid #e1e4ea;
}
.access-stats {
  display: flex;
  gap: 2rem;
  margin: 0.5rem 0;
  flex-wrap: wrap;
  justify-content: flex-start;
}
.stat {
  text-align: center;
  min-width: 70px;
}
.stat .label {
  display: block;
}
.stat .value {
  display: block;
  font-weight: bold;
  color: #2c3e50;
  font-size: 1.15em;
}
.access-link {
  font-size: 0.85em;
  margin-left: 0.5rem;
  color: #3498db;
  text-decoration: underline;
}
.access-link:hover {
  color: #217dbb;
}
.nav-tabs {
  border-bottom: 2px solid #e1e4ea;
}
.nav-tabs .nav-link {
  border: none;
  border-radius: 0;
  color: #888;
  background: none;
  font-weight: 500;
  padding: 0.7em 1.5em;
  transition: color 0.2s, background 0.2s;
}
.nav-tabs .nav-link.active, .nav-tabs .nav-link:hover {
  color: #3498db;
  background: #f4f6fa;
  border-bottom: 2px solid #3498db;
}
.tab-content {
  background: #fff;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 2px 8px rgba(44,62,80,0.04);
  padding: 1.5rem 1rem 1rem 1rem;
  margin-top: -2px;
}
.course-activity-chart {
  background: #f8f9fa;
  border-radius: 6px;
  box-shadow: 0 1px 2px rgba(44,62,80,0.04);
  margin: 0 auto;
  display: block;
}
@media (max-width: 700px) {
  .course-activity-chart {
    width: 100% !important;
    height: 180px !important;
  }
  .tab-content {
    padding: 1rem 0.2rem;
  }
  .access-stats {
    gap: 1rem;
  }
}
.badge.bg-danger {
  background: #e74c3c;
  color: #fff;
  font-size: 0.85em;
  padding: 0.3em 0.7em;
  border-radius: 12px;
  margin-left: 0.2em;
}
 .teacher-activity-container {
    min-height: 100vh;
    padding: 1rem;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  }

  .modern-header {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }

  .modern-header h1 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2d5b3f;
    margin: 0 0 0.5rem 0;
  }

  .modern-header .subtitle {
    font-size: 0.95rem;
    color: #666;
    margin: 0;
  }

  .course-section {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }

  .course-header {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f4a534;
  }

  .course-title {
    font-size: 1.4rem;
    font-weight: 600;
    color: #2d5b3f;
    margin: 0;
  }

  .alert-section {
    background: linear-gradient(135deg, #f4a534 0%, #e69500 100%);
    color: white;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 8px rgba(244, 165, 52, 0.3);
  }

  .alert-section h3 {
    margin: 0 0 0.75rem 0;
    font-size: 1.1rem;
    font-weight: 600;
  }

  .alert-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .alert-item {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.4rem 0.8rem;
    border-radius: 6px;
    font-size: 0.85rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
  }

  .students-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .student-card {
    background: white;
    border-radius: 8px;
    padding: 1.25rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid #e0e0e0;
    transition: all 0.2s ease;
    position: relative;
  }

  .student-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .student-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
  }

  .student-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2d5b3f;
    text-decoration: none;
  }

  .student-name:hover {
    color: #f4a534;
  }

  .student-actions {
    display: flex;
    gap: 0.4rem;
  }

  .action-btn, .message_student_link {
    background: transparent;
    color: #2d5b3f;
    border: none;
    padding: 0.4rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    font-size: 1rem;
  }

  .action-btn:hover, .message_student_link:hover {
    color: #f4a534;
    transform: scale(1.1);
  }

  .student-metrics {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
    margin-bottom: 1rem;
  }

  .metric {
    text-align: center;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
    position: relative;
  }

  .metric-value {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2d5b3f;
    margin-bottom: 0.25rem;
  }

  .metric-label {
    font-size: 0.8rem;
    color: #666;
  }

  .progress-bar {
    width: 100%;
    height: 4px;
    background: #e0e0e0;
    border-radius: 2px;
    overflow: hidden;
    margin-top: 0.4rem;
  }

  .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #2d5b3f 0%, #f4a534 100%);
    transition: width 0.3s ease;
  }

  .access-summary {
    background: #f8f9fa;
    padding: 0.75rem;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
    margin-bottom: 1rem;
  }

  .access-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.75rem;
    margin-bottom: 0.75rem;
  }

  .stat {
    text-align: center;
  }

  .stat .value {
    display: block;
    font-weight: 600;
    color: #2d5b3f;
    font-size: 1.1rem;
  }

  .last-interaction {
    font-size: 0.8rem;
    color: #888;
    margin-bottom: 0.5rem;
  }

  .access-link {
    font-size: 0.85em;
    margin-left: 0.5rem;
    color: #3498db;
    text-decoration: underline;
  }

  .access-link:hover {
    color: #217dbb;
  }

  .student-flags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.4rem;
  }

  .flag {
    padding: 0.25rem 0.6rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
  }

  .flag.critical {
    background: #dc3545;
    color: white;
  }

  .flag.warning {
    background: #f4a534;
    color: white;
  }

  .flag.good {
    background: #2d5b3f;
    color: white;
  }

  .ungraded-assignments {
    margin-top: 1rem;
    padding: 0.75rem;
    background: #fff4e6;
    border-radius: 6px;
    border-left: 3px solid #f4a534;
  }

  .ungraded-title {
    font-weight: 600;
    color: #b8860b;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
  }

  .assignment-link {
    display: block;
    color: #2d5b3f;
    text-decoration: none;
    margin-bottom: 0.25rem;
    font-size: 0.85rem;
  }

  .assignment-link:hover {
    color: #f4a534;
  }

  .assignment-date {
    font-size: 0.75rem;
    color: #666;
    margin-left: 0.5rem;
    margin-bottom: 0.4rem;
  }

  /* Chart Section */
  .charts-section {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-top: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }

  .charts-header {
    text-align: center;
    margin-bottom: 1.5rem;
  }

  .charts-header h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2d5b3f;
    margin: 0 0 0.5rem 0;
  }

  .charts-header p {
    color: #666;
    font-size: 0.9rem;
    margin: 0;
  }

  .nav-tabs {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    border: none;
  }

  .nav-link {
    background: transparent;
    border: 2px solid #e0e0e0;
    color: #666;
    padding: 0.6rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
    text-decoration: none;
    font-size: 0.9rem;
  }

  .nav-link.active, .nav-link:hover {
    background: #2d5b3f;
    color: white;
    border-color: #2d5b3f;
  }

  .tab-content {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 1rem;
    min-height: 250px;
    border: 1px solid #e0e0e0;
  }

  .course-activity-chart {
    background: transparent;
    border-radius: 6px;
    margin: 0 auto;
    display: block;
    max-width: 100%;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .teacher-activity-container {
      padding: 0.75rem;
    }
    
    .students-grid {
      grid-template-columns: 1fr;
    }
    
    .course-header {
      align-items: flex-start;
    }

    .access-stats {
      gap: 1rem;
    }

    .nav-tabs {
      flex-direction: column;
      align-items: center;
    }
  }

  /* Subtle animations */
  .student-card {
    animation: fadeInUp 0.3s ease-out;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Brand Colors */
  :root {
    --primary-green: #2d5b3f;
    --secondary-orange: #f4a534;
    --light-green: #b8d4c2;
    --dark-green: #1a3d2b;
  }
  .activity-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e1e4ea;
}

.summary-card {
  text-align: center;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.summary-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.summary-card .icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  display: block;
}

.summary-card .value {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2d5b3f;
  margin-bottom: 0.25rem;
  display: block;
}

.summary-card .label {
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
}

.metric-description {
  background: white;
  padding: 1rem;
  border-radius: 6px;
  margin-bottom: 1rem;
  border-left: 4px solid #2d5b3f;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.metric-description h4 {
  margin: 0 0 0.5rem 0;
  color: #2d5b3f;
  font-size: 1.1rem;
  font-weight: 600;
}

.metric-description p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
}

.chart-container {
  position: relative;
  background: white;
  border-radius: 6px;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  margin-bottom: 1rem;
}

.metric-stats {
  display: flex;
  justify-content: space-around;
  margin-top: 1rem;
  padding: 1rem;
  background: white;
  border-radius: 6px;
  font-size: 0.85rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.metric-stat {
  text-align: center;
  flex: 1;
}

.metric-stat .stat-value {
  display: block;
  font-weight: 600;
  color: #2d5b3f;
  font-size: 1.2rem;
  margin-bottom: 0.25rem;
}

.metric-stat .stat-label {
  color: #666;
  font-size: 0.8rem;
  font-weight: 500;
}

.insight-box {
  background: linear-gradient(135deg, #2d5b3f 0%, #3d6b4f 100%);
  color: white;
  padding: 1rem;
  border-radius: 6px;
  margin-top: 1rem;
  box-shadow: 0 2px 6px rgba(45, 91, 63, 0.3);
}

.insight-box h5 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.insight-box p {
  margin: 0;
  font-size: 0.9rem;
  opacity: 0.95;
  line-height: 1.4;
}

/* Enhanced nav tabs */
.nav-tabs {
  background: #f8f9fa;
  padding: 0.5rem;
  border-radius: 8px;
  border: 1px solid #e1e4ea;
  margin-bottom: 1.5rem;
}

.nav-link {
  background: transparent;
  border: none;
  color: #666;
  padding: 0.8rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
  text-decoration: none;
  font-size: 0.95rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: center;
}

.nav-link.active {
  background: #2d5b3f;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(45, 91, 63, 0.3);
}

.nav-link:hover:not(.active) {
  background: white;
  color: #2d5b3f;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Enhanced tab content */
.tab-content {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  min-height: 400px;
  border: 1px solid #e1e4ea;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

/* Updated charts section */
.charts-section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin-top: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #e1e4ea;
}

.charts-header {
  text-align: center;
  margin-bottom: 2rem;
}

.charts-header h3 {
  font-size: 1.4rem;
  font-weight: 600;
  color: #2d5b3f;
  margin: 0 0 0.5rem 0;
}

.charts-header p {
  color: #666;
  font-size: 1rem;
  margin: 0;
}

/* Responsive design */
@media (max-width: 768px) {
  .activity-summary {
    grid-template-columns: repeat(2, 1fr);
    padding: 1rem;
  }
  
  .metric-stats {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .metric-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 4px;
  }
  
  .metric-stat .stat-value {
    font-size: 1.1rem;
    margin-bottom: 0;
  }
  
  .charts-section {
    padding: 1rem;
  }
  
  .nav-tabs {
    flex-direction: column;
    align-items: stretch;
  }
  
  .nav-link {
    text-align: center;
    margin-bottom: 0.25rem;
  }
}

@media (max-width: 480px) {
  .activity-summary {
    grid-template-columns: 1fr;
  }
  
  .summary-card .icon {
    font-size: 1.5rem;
  }
  
  .summary-card .value {
    font-size: 1.3rem;
  }
}

