<%
# Copyright (C) 2011 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHANT<PERSON>ILITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.
%>
<%
  css_bundle :profile_show, :tinymce
  js_bundle :profile_show
  add_body_class 'not-editing'
  js_env PROFILE: @user_data if @user_data[:can_edit]
  js_env(folder_id: @user.profile_pics_folder.id) if @user == @current_user

  unless @read_only
    # Handle different context types for profile vs admin pages
    if @context.is_a?(Account)
      account = @context
    elsif @context.respond_to?(:account)
      account = @context.account
    else
      account = nil
    end
    
    managed_accounts = @user.associated_root_accounts.select{|a| can_do(a, @current_user, :allow_course_admin_actions) }
    account ||= managed_accounts.first
    root_account = account&.root_account || account
  end
  login_history ||= nil
  
  # Profile show variables (from your shared code)
  is_roster_page = is_roster_user_page ||= false
  inst_env_feature_enabled = @domain_root_account&.feature_enabled?(:instui_nav)
  legacy_view = !inst_env_feature_enabled || is_roster_page ? "legacy" : ""
  image_size = inst_env_feature_enabled && !is_roster_page ? 80 : 128
  primary_button = inst_env_feature_enabled ? "btn btn-primary" : ""
%>

<% js_bundle :user_name %>
<% css_bundle :user_profile %>

<div class="student-profile-header">
  <div class="student-avatar">
    <% if service_enabled?(:avatars) %>
      <div class="profile-avatar-wrapper">
        <% if @user_data[:can_edit_avatar] %>
          <%= avatar(@user_data[:id], size: 80, url: '#', edit: true, class: "profile-link",
                     sr_content: t('Click to change profile picture for %{display_name}', :display_name => @user.short_name))
          %>
        <% else %>
          <%= avatar @user, size: 80, url: nil %>
        <% end %>
      </div>
    <% end %>
  </div>
  
  <div class="student-info">
    <h1 class="student-name"><%=h @user.name %></h1>
    <div class="student-details">
      <% details = [] %>
      
      <% if @user.respond_to?(:student) && @user.student&.gender.present? %>
        <% details << @user.student.gender %>
      <% end %>
      
      <% if @user.pseudonyms.first&.sis_user_id.present? %>
        <% details << @user.pseudonyms.first.sis_user_id %>
      <% end %>
      
      <!-- Display all details in separate divs -->
      <% details.each do |detail| %>
        <div><%=h detail %></div>
      <% end %>
    </div>
  </div>
  
  <div class="student-academic-info">
    <% if @user.respond_to?(:student) && @user.student&.semester.present? %>
      <div class="semester-info">
        <% if term = EnrollmentTerm.find_by(id: @user.student.semester) %>
          <%=h term.name %>
        <% else %>
          <%=h @user.student.semester %>
        <% end %>
      </div>
    <% end %>
    
    <!-- Display Department under term -->
    <% if @user.department.present? %>
      <div class="department-info">
        <% if @user.department.match?(/^\d+$/) %>
          <!-- If department is stored as ID, look up the department name -->
          <% if defined?(Department) && (dept = Department.find_by(id: @user.department)) %>
            <%=h dept.name %>
          <% else %>
            <%=h @user.department %>
          <% end %>
        <% else %>
          <!-- If department is stored as name or string -->
          <%=h @user.department %>
        <% end %>
      </div>
    <% end %>
    
    <!-- Display Current Year standing based on active enrollments -->
    <% if @user.enrollments.active.any? %>
      <div class="current-semester-info">
        <% 
          # Get the current semester from curriculum programs based on active enrollments
          current_semester_display = nil
          
          # Get active course codes
          active_course_codes = @user.enrollments.active.joins(:course).pluck('courses.course_code')
          
          if active_course_codes.any? && @user.department.present?
            # Try to find department
            user_department = nil
            if @user.department.to_s.match?(/^\d+$/)
              user_department = Department.find_by(id: @user.department) if defined?(Department)
            end
            
            if user_department.nil? && defined?(Department)
              user_department = Department.find_by(name: @user.department)
            end
            
            if user_department.nil? && defined?(Department)
              user_department = Department.where("name ILIKE ?", "%#{@user.department}%").first
            end
            
            if user_department
              # Get curriculum programs for this department
              curriculum_programs = []
              if user_department.respond_to?(:curriculum_programs)
                curriculum_programs = user_department.curriculum_programs
              elsif defined?(CurriculumProgram)
                curriculum_programs = CurriculumProgram.where(department: user_department)
              end
              
              # Find all curriculum programs that match active courses
              active_curricula = curriculum_programs.select { |cp| active_course_codes.include?(cp.course_code) }
              
              if active_curricula.any?
                # Get all unique semesters from active curricula
                active_semesters = active_curricula.map(&:semester).uniq
                
                # Define the exact semester order (same as in curriculum section)
                semester_order = [
                  "1st Year - 1st Sem", "1st Year - 2nd Sem", "1st Year - 3rd Sem", "1st Year - Summer",
                  "2nd Year - 1st Sem", "2nd Year - 2nd Sem", "2nd Year - 3rd Sem", "2nd Year - Summer",
                  "3rd Year - 1st Sem", "3rd Year - 2nd Sem", "3rd Year - 3rd Sem", "3rd Year - Summer",
                  "4th Year - 1st Sem", "4th Year - 2nd Sem", "4th Year - 3rd Sem", "4th Year - Summer"
                ]
                
                # Find the first semester in the order that matches active semesters
                selected_semester = semester_order.find { |sem| active_semesters.include?(sem) }
                
                # If no exact match, try flexible matching
                if selected_semester.nil?
                  semester_order.each do |semester_label|
                    matching_semester = active_semesters.find do |active_semester|
                      normalized_existing = active_semester.to_s.strip
                      normalized_target = semester_label.strip
                      existing_parts = normalized_existing.split(/\s*-\s*/)
                      target_parts = normalized_target.split(/\s*-\s*/)
                      
                      if existing_parts.length >= 2 && target_parts.length >= 2
                        year_match = existing_parts[0].strip.downcase == target_parts[0].strip.downcase
                        existing_sem = existing_parts[1].strip.downcase
                        target_sem = target_parts[1].strip.downcase
                        
                        sem_match = if target_sem.include?('summer')
                          existing_sem.include?('summer')
                        elsif target_sem.include?('1st')
                          existing_sem.include?('1st') || existing_sem.include?('first') || existing_sem == '1'
                        elsif target_sem.include?('2nd')
                          existing_sem.include?('2nd') || existing_sem.include?('second') || existing_sem == '2'
                        elsif target_sem.include?('3rd')
                          existing_sem.include?('3rd') || existing_sem.include?('third') || existing_sem == '3'
                        else
                          false
                        end
                        
                        year_match && sem_match
                      else
                        false
                      end
                    end
                    
                    if matching_semester
                      selected_semester = matching_semester
                      break
                    end
                  end
                end
                
                # Use first active semester if still no match
                selected_semester ||= active_semesters.first
                
                # Convert semester to year display format
                if selected_semester.present?
                  case selected_semester.to_s.strip.downcase
                  when /1st year/
                    current_semester_display = "FIRST YEAR"
                  when /2nd year/
                    current_semester_display = "SECOND YEAR"
                  when /3rd year/
                    current_semester_display = "THIRD YEAR"
                  when /4th year/
                    current_semester_display = "FOURTH YEAR"
                  else
                    # Fallback: extract year number and convert
                    if selected_semester.match(/(\d+)/)
                      year_num = selected_semester.match(/(\d+)/)[1].to_i
                      current_semester_display = case year_num
                                                when 1 then "FIRST YEAR"
                                                when 2 then "SECOND YEAR"
                                                when 3 then "THIRD YEAR"
                                                when 4 then "FOURTH YEAR"
                                                else selected_semester
                                                end
                    else
                      current_semester_display = selected_semester
                    end
                  end
                end
              end
            end
          end
          
          # Fallback to enrollment term if no curriculum match found
          if current_semester_display.nil?
            current_enrollment = @user.enrollments.active.joins(:course).preload(course: :enrollment_term).first
            
            if current_enrollment&.course&.enrollment_term
              current_semester_display = current_enrollment.course.enrollment_term.name
            elsif current_enrollment&.course
              current_semester_display = "Current Term"
            else
              current_semester_display = "Active Enrollment"
            end
          end
        %>
        <%=h current_semester_display %>
      </div>
    <% elsif @user.respond_to?(:student) && @user.student&.semester.present? %>
      <!-- Fallback: Use semester from student record if no active enrollments -->
      <div class="current-semester-info">
        <% if term = EnrollmentTerm.find_by(id: @user.student.semester) %>
          <%=h term.name %>
        <% else %>
          <%=h @user.student.semester %>
        <% end %>
      </div>
    <% end %>
  </div>
</div>

<div class="profile-tabs">
  <button class="tab-button active" onclick="showTab('subjects-enrolled')">
    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
      <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
    </svg>
    Subjects Enrolled
  </button>
  <button class="tab-button" onclick="showTab('program-curriculum')">
    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
    </svg>
    Program Curriculum
  </button>
  <button class="tab-button" onclick="showTab('badges-achievements')">
    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
      <path d="M5,16L3,5L8.5,12L12,6L15.5,12L21,5L19,16H5M12,18A2,2 0 0,1 14,20A2,2 0 0,1 12,22A2,2 0 0,1 10,20A2,2 0 0,1 12,18Z"/>
    </svg>
    <span>Badges & Achievements</span>
    <span class="badge-count">(<%=h @user.user_badges.count %>)</span>
  </button>
</div>

<div id="subjects-enrolled" class="tab-content active">
  <% active_enrollments = @user.enrollments.active.to_a %>
  <% user_roles = active_enrollments.map(&:type).uniq %>
  <% is_teacher = user_roles.include?("TeacherEnrollment") %>
  <% if active_enrollments.any? %>
    <% enrollments_by_term = active_enrollments.group_by { |e| e.course&.enrollment_term } %>
    <% enrollments_by_term.each do |term, enrollments| %>
      <% if enrollments_by_term.size > 1 %>
        <h3 class="section-header">
          <% if term %>
            <%=h term.name %>
          <% else %>
            <%= t('current_term', 'Current Term') %>
          <% end %>
        </h3>
      <% end %>
      
      <table class="enrollment-table">
        <thead>
          <tr>
            <th>Course Code</th>
            <th>Course Title</th>
            <th>Units</th>
            <th>Section</th>
            <th>Instructor</th>
            <th>Schedules</th>
          </tr>
        </thead>
        <tbody>
          <% enrollments.each do |enrollment| %>
            <% course = enrollment.course %>
            <% next unless course %>
            <tr>
              <td><%=h course.course_code %></td>
              <td><%=h course.name %></td>
              <td>
                <% if course.respond_to?(:units) && course.units.present? %>
                  <%=h course.units %>
                <% elsif course.settings&.dig('units').present? %>
                  <%=h course.settings['units'] %>
                <% else %>
                  3
                <% end %>
              </td>
              <td><%=h enrollment.course_section&.name || course.course_code %></td>
              <td>
                <% teacher_enrollment = course.enrollments.where(type: 'TeacherEnrollment').first %>
                <% if teacher_enrollment&.user %>
                  <%=h teacher_enrollment.user.name %>
                <% else %>
                  <%= t('tbd', 'TBD') %>
                <% end %>
              </td>
              <td>
                <%= course.display_schedule %>
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>
    <% end %>
  <% else %>
    <p><%= t('no_enrollments', 'No current enrollments found.') %></p>
  <% end %>
</div>

<div id="program-curriculum" class="tab-content">
  <% if @user.department.present? %>
    <% 
      # Get the user's department
      user_department = nil
      
      # Try to find department by ID first
      if @user.department.to_s.match?(/^\d+$/)
        user_department = Department.find_by(id: @user.department) if defined?(Department)
      end
      
      # If not found by ID, try by name
      if user_department.nil? && defined?(Department)
        user_department = Department.find_by(name: @user.department)
      end
      
      # If still not found, try partial match
      if user_department.nil? && defined?(Department)
        user_department = Department.where("name ILIKE ?", "%#{@user.department}%").first
      end
      
      # Determine if current user is NOT a student (only students should see grades)
      hide_grades = true
      
      # Check if current user is a student (has only StudentEnrollment)
      current_user_enrollments = @current_user.enrollments.active
      user_roles = current_user_enrollments.pluck(:type).uniq
      
      # Only show grades if user has StudentEnrollment and no teacher/admin roles
      if user_roles.include?('StudentEnrollment') && 
         !user_roles.include?('TeacherEnrollment') && 
         !user_roles.include?('TaEnrollment') &&
         !@current_user.account_users.active.exists?
        hide_grades = false
      end
      
      # Double-check: if user has any administrative permissions, hide grades
      if @context && (can_do(@context, @current_user, :manage_content) || 
                     can_do(@context, @current_user, :read_reports) ||
                     can_do(@context, @current_user, :view_all_grades))
        hide_grades = true
      end
      
      # Check if user has site admin privileges
      if @current_user.account.present? && @current_user.account.site_admin?
        hide_grades = true
      end
    %>
    
    <% if user_department %>
      <% 
        # Get curriculum programs for this department
        curriculum_programs = []
        if user_department.respond_to?(:curriculum_programs)
          curriculum_programs = user_department.curriculum_programs.order(:semester, :course_code)
        elsif defined?(CurriculumProgram)
          curriculum_programs = CurriculumProgram.where(department: user_department).order(:semester, :course_code)
        end
        
        # Define the exact semester order
        semester_order = [
          "1st Year - 1st Sem", "1st Year - 2nd Sem", "1st Year - 3rd Sem", "1st Year - Summer",
          "2nd Year - 1st Sem", "2nd Year - 2nd Sem", "2nd Year - 3rd Sem", "2nd Year - Summer",
          "3rd Year - 1st Sem", "3rd Year - 2nd Sem", "3rd Year - 3rd Sem", "3rd Year - Summer",
          "4th Year - 1st Sem", "4th Year - 2nd Sem", "4th Year - 3rd Sem", "4th Year - Summer"
        ]
        
        # Group curriculum programs by semester
        programs_by_semester = curriculum_programs.group_by(&:semester)
        
        # Create organized programs hash following the exact order
        organized_programs = {}
        semester_order.each do |semester_label|
          if programs_by_semester[semester_label]
            organized_programs[semester_label] = programs_by_semester[semester_label]
          else
            # Try to find similar matches for different naming conventions
            matching_semester = programs_by_semester.keys.find do |existing_semester|
              normalized_existing = existing_semester.to_s.strip
              normalized_target = semester_label.strip
              existing_parts = normalized_existing.split(/\s*-\s*/)
              target_parts = normalized_target.split(/\s*-\s*/)
              
              if existing_parts.length >= 2 && target_parts.length >= 2
                year_match = existing_parts[0].strip.downcase == target_parts[0].strip.downcase
                existing_sem = existing_parts[1].strip.downcase
                target_sem = target_parts[1].strip.downcase
                
                sem_match = if target_sem.include?('summer')
                  existing_sem.include?('summer')
                elsif target_sem.include?('1st')
                  existing_sem.include?('1st') || existing_sem.include?('first') || existing_sem == '1'
                elsif target_sem.include?('2nd')
                  existing_sem.include?('2nd') || existing_sem.include?('second') || existing_sem == '2'
                elsif target_sem.include?('3rd')
                  existing_sem.include?('3rd') || existing_sem.include?('third') || existing_sem == '3'
                else
                  false
                end
                
                year_match && sem_match
              else
                false
              end
            end
            
            if matching_semester
              organized_programs[semester_label] = programs_by_semester[matching_semester]
            end
          end
        end
        
        # Add any remaining semesters that don't match the standard pattern
        programs_by_semester.each do |semester, programs|
          unless organized_programs.values.include?(programs)
            organized_programs[semester] = programs
          end
        end

        # Pre-fetch all enrollments and submissions for efficiency
        user_enrollments = @user.enrollments
          .joins(:course)
          .where(workflow_state: ['active', 'completed', 'inactive'])
          .preload(:course, :scores)
          .index_by { |e| e.course.course_code }

        # Get all course codes from curriculum
        all_course_codes = curriculum_programs.map(&:course_code).uniq
        
        # Fetch courses by course codes for grade lookup
        curriculum_courses = Course.where(course_code: all_course_codes)
          .preload(:enrollments, :assignment_groups, :assignments)
          .index_by(&:course_code)

        # Pre-fetch all submissions for the user in these courses
        user_submissions = @user.submissions
          .joins(assignment: :course)
          .where(assignments: { courses: { course_code: all_course_codes } })
          .preload(:assignment, :rubric_assessment, :submission_comments)
          .group_by { |s| s.assignment.course.course_code }
      %>
      
      <% if organized_programs.any? %>
        <% organized_programs.each do |semester_label, programs| %>
          <h3 class="section-header"><%=h semester_label %></h3>
          
          <!-- Role-based curriculum table structure -->
          <table class="enrollment-table curriculum-table">
            <thead>
              <tr>
                <th>Course Code</th>
                <th>Course Title</th>
                <th>Units</th>
                <%# Only show Grade and Remarks if current user is a student %>
                <% unless hide_grades %>
                  <th>Grade</th>
                  <th>Remarks</th>
                <% end %>
                <th>Prerequisite/Co-requisite</th>
              </tr>
            </thead>
            <tbody>
              <% programs.each do |program| %>
                <% 
                  # Get Canvas enrollment and grade data
                  enrollment = user_enrollments[program.course_code]
                  course = curriculum_courses[program.course_code]
                  course_submissions = user_submissions[program.course_code] || []
                  
                  final_score = nil
                  letter_grade = nil
                  grade_status = 'not-taken'
                  status_text = 'Not Taken'
                  course_state = 'inactive'
                  
                  # Grade calculation logic
                  if enrollment
                    current_score = enrollment.scores.where(workflow_state: 'active').first
                    
                    if course_submissions.any?
                      total_points_earned = 0
                      total_points_possible = 0
                      
                      course_submissions.each do |submission|
                        assignment = submission.assignment
                        next unless assignment && assignment.points_possible.to_f > 0
                        
                        if submission.score.present? && !submission.excused? && submission.graded?
                          total_points_earned += submission.score
                          total_points_possible += assignment.points_possible
                        end
                      end
                      
                      if total_points_possible > 0
                        final_score = (total_points_earned / total_points_possible * 100).round(1)
                      end
                    elsif current_score&.final_score.present?
                      final_score = current_score.final_score
                    end
                    
                    # Grade conversion logic
                    if enrollment.computed_final_grade.present? && 
                       ['1.00', '1.25', '1.50', '1.75', '2.00', '2.25', '2.50', '2.75', '3.00', '4.00', '5.00'].include?(enrollment.computed_final_grade)
                      letter_grade = enrollment.computed_final_grade
                    elsif current_score&.final_grade.present? && 
                          ['1.00', '1.25', '1.50', '1.75', '2.00', '2.25', '2.50', '2.75', '3.00', '4.00', '5.00'].include?(current_score.final_grade)
                      letter_grade = current_score.final_grade
                    elsif final_score.present?
                      letter_grade = case final_score
                        when 99..100 then '1.00'
                        when 96..98 then '1.25'
                        when 93..95 then '1.50'
                        when 89..92 then '1.75'
                        when 85..88 then '2.00'
                        when 82..84 then '2.25'
                        when 79..81 then '2.50'
                        when 76..78 then '2.75'
                        when 75 then '3.00'
                        when 70..74 then '4.00'
                        else '5.00'
                      end
                    end
                    
                    # Status determination logic
                    case enrollment.workflow_state
                    when 'active'
                      if letter_grade.present?
                        case letter_grade.to_s
                        when '1.00', '1.25', '1.50', '1.75', '2.00', '2.25', '2.50', '2.75', '3.00'
                          grade_status = 'passed'
                          status_text = 'Passed'
                        when '4.00'
                          grade_status = 'incomplete'
                          status_text = 'INC'
                        when '5.00'
                          grade_status = 'failing'
                          status_text = 'Failing'
                        else
                          grade_status = 'in-progress'
                          status_text = 'In Progress'
                        end
                      else
                        grade_status = 'in-progress'
                        status_text = 'In Progress'
                      end
                    when 'completed'
                      if letter_grade.present?
                        case letter_grade.to_s
                        when '1.00', '1.25', '1.50', '1.75', '2.00', '2.25', '2.50', '2.75', '3.00'
                          grade_status = 'passed'
                          status_text = 'Completed'
                        when '4.00'
                          grade_status = 'incomplete'
                          status_text = 'INC'
                        when '5.00'
                          grade_status = 'failed'
                          status_text = 'Failed'
                        else
                          grade_status = 'completed-no-grade'
                          status_text = 'Completed'
                        end
                      else
                        grade_status = 'completed-no-grade'
                        status_text = 'Completed'
                      end
                    when 'inactive'
                      grade_status = 'dropped'
                      status_text = 'Dropped'
                    end
                  end
                %>
                
                <tr class="curriculum-row" 
                    data-course-code="<%=h program.course_code %>" 
                    data-status="<%= grade_status %>">
                  <td class="course-code-cell"><%=h program.course_code %></td>
                  <td class="course-title-cell"><%=h program.course_description || program.course_title %></td>
                  <td class="units-cell"><%=h program.units %></td>
                  
                  <% unless hide_grades %>
                    <!-- Student view: Show grade and remarks -->
                    <td class="letter-grade-cell">
                      <% if letter_grade.present? %>
                        <% case grade_status %>
                        <% when 'passed' %>
                          <span class="grade-display grade-passed"><%=h letter_grade %></span>
                        <% when 'failed', 'failing', 'incomplete' %>
                          <span class="grade-display grade-failed"><%=h letter_grade %></span>
                        <% else %>
                          <span class="grade-display"><%=h letter_grade %></span>
                        <% end %>
                      <% else %>
                        <span class="no-letter-grade">--</span>
                      <% end %>
                    </td>
                    <td class="status-cell">
                      <span class="status-badge <%= grade_status %>">
                        <% case grade_status %>
                        <% when 'passed' %>
                          <i class="icon-check"></i> <%= status_text %>
                        <% when 'failed', 'failing' %>
                          <i class="icon-x"></i> Failed
                        <% when 'incomplete' %>
                          <i class="icon-clock"></i> INC
                        <% when 'in-progress' %>
                          <i class="icon-clock"></i> <%= status_text %>
                        <% when 'dropped' %>
                          <i class="icon-minimize"></i> <%= status_text %>
                        <% when 'not-enrolled' %>
                          <i class="icon-user"></i> <%= status_text %>
                        <% when 'completed-no-grade' %>
                          <i class="icon-complete"></i> <%= status_text %>
                        <% else %>
                          <i class="icon-empty"></i> <%= status_text %>
                        <% end %>
                      </span>
                    </td>
                  <% end %>
                  <td class="prerequisite-cell">
                    <% if program.prerequisite.present? && program.prerequisite != '-' && program.prerequisite.downcase != 'none' %>
                      <span class="prerequisite-text"><%=h program.prerequisite %></span>
                    <% else %>
                      <span class="no-prerequisite">None</span>
                    <% end %>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
                      
          <!-- Semester Summary - only show for students -->
          <% unless hide_grades %>
            <% 
              # Gather semester enrollments and calculate stats
              semester_enrollments = programs.map { |p| user_enrollments[p.course_code] }.compact
              total_units = programs.sum(&:units)
              completed_units = programs.select { |p| 
                enrollment = user_enrollments[p.course_code]
                if enrollment&.workflow_state == 'completed'
                  if enrollment.computed_final_grade.present?
                    !['4.00', '5.00'].include?(enrollment.computed_final_grade)
                  else
                    score = enrollment.scores.where(workflow_state: 'active').first
                    score&.final_score.present? && score.final_score >= 75.0
                  end
                else
                  false
                end
              }.sum(&:units)

              # Calculate semester GPA
              semester_gpa = if semester_enrollments.any?
                total_points = 0
                total_credits = 0

                semester_enrollments.each do |enrollment|
                  score = enrollment.scores.where(workflow_state: 'active').first
                  if score&.final_score.present?
                    grade_points = case score.final_score
                      when 97..100 then 4.0
                      when 93..96  then 3.7
                      when 90..92  then 3.3
                      when 87..89  then 3.0
                      when 83..86  then 2.7
                      when 80..82  then 2.3
                      when 77..79  then 2.0
                      when 75..76  then 1.7
                      when 65..74  then 1.0
                      else 0.0
                    end

                    program = programs.find { |p| user_enrollments[p.course_code] == enrollment }
                    if program
                      total_points += grade_points * program.units
                      total_credits += program.units
                    end
                  end
                end

                total_credits > 0 ? (total_points / total_credits).round(2) : nil
              end
            %>
            
            <div class="semester-summary">
              <div class="summary-stats">
                <div class="stat-item">
                  <span class="stat-label">Total Units:</span>
                  <span class="stat-value"><%= total_units %></span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">Completed Units:</span>
                  <span class="stat-value"><%= completed_units %></span>
                </div>
                <% if semester_gpa %>
                  <div class="stat-item">
                    <span class="stat-label">Semester GPA:</span>
                    <span class="stat-value gpa-<%= semester_gpa >= 3.0 ? 'good' : (semester_gpa >= 2.0 ? 'fair' : 'poor') %>">
                      <%= semester_gpa %>
                    </span>
                  </div>
                <% end %>
              </div>
            </div>
          <% end %>
        <% end %>
        
        <%# Overall Summary - only show for students %>
        <% unless hide_grades %>
          <% 
            all_enrollments = user_enrollments.values
            overall_gpa = if all_enrollments.any?
              total_points = 0
              total_credits = 0

              all_enrollments.each do |enrollment|
                score = enrollment.scores.where(workflow_state: 'active').first
                if score&.final_score.present?
                  grade_points = case score.final_score
                    when 97..100 then 4.0
                    when 93..96  then 3.7
                    when 90..92  then 3.3
                    when 87..89  then 3.0
                    when 83..86  then 2.7
                    when 80..82  then 2.3
                    when 77..79  then 2.0
                    when 75..76  then 1.7
                    when 65..74  then 1.0
                    else 0.0
                  end

                  program = curriculum_programs.find { |p| p.course_code == enrollment.course.course_code }
                  if program
                    total_points += grade_points * program.units
                    total_credits += program.units
                  end
                end
              end

              total_credits > 0 ? (total_points / total_credits).round(2) : nil
            end

            total_curriculum_units = curriculum_programs.sum(&:units)
            completed_curriculum_units = curriculum_programs.select { |p| 
              enrollment = user_enrollments[p.course_code]
              if enrollment&.workflow_state == 'completed'
                if enrollment.computed_final_grade.present?
                  !['4.00', '5.00'].include?(enrollment.computed_final_grade)
                else
                  score = enrollment.scores.where(workflow_state: 'active').first
                  score&.final_score.present? && score.final_score >= 75.0
                end
              else
                false
              end
            }.sum(&:units)
          %>
        <% end %>
        
      <% else %>
        <div class="empty-state no-curriculum">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
            <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
          </svg>
          <h4>No Curriculum Available</h4>
          <p>No curriculum programs have been added for <strong><%=h user_department.name %></strong> yet.</p>
        </div>
      <% end %>
    <% else %>
      <div class="empty-state department-not-found">
        <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
        </svg>
        <h4>Department Not Found</h4>
        <p>Could not find department information for: <strong><%=h @user.department %></strong></p>
        <p class="help-text">Please contact your administrator to verify your department assignment.</p>
      </div>
    <% end %>
  <% else %>
    <div class="empty-state no-department">
      <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
      </svg>
      <h4>No Department Assigned</h4>
      <p>No department information is available for your profile.</p>
      <p class="help-text">Please contact your administrator to assign a department to your account.</p>
    </div>
  <% end %>
</div>

<div id="badges-achievements" class="tab-content">
  <div class="badges-header">
    <% if @user == @current_user && @user.user_badges.any? %>
      <div class="badges-header-content">
        <button class="add-new-badge-btn" onclick="showAddBadgeModal()">+ Add New Badge</button>
      </div>
    <% end %>
  </div>

  <%
    # Get user's badges with privacy considerations
    user_badges = @user.user_badges.preload(:badge).recent

    # Show different badges based on viewer
    if @user == @current_user
      # Own profile: show all badges
      visible_badges = user_badges
      can_edit_privacy = true
    else
      # Other's profile: only show public badges
      visible_badges = user_badges.where(is_public: true)
      can_edit_privacy = false
    end
  %>

  <% if visible_badges.any? %>
    <div class="badges-grid">
      <% visible_badges.each do |user_badge| %>
        <div class="badge-item <%= 'private-badge' unless user_badge.is_public? %>">
          <div class="badge-icon">
            <% if user_badge.notes.present? && (user_badge.notes.start_with?('/uploads/') || user_badge.notes.match?(/\.(jpg|jpeg|png|gif|pdf)$/i)) %>
              <!-- Show the actual certificate image -->
              <% 
                image_url = if user_badge.notes.start_with?('/uploads/')
                              user_badge.notes  # New format: "/uploads/badges/filename.jpg"
                            else
                              "/uploads/badges/#{user_badge.notes}"  # Old format: "filename.jpg"
                            end
              %>
              <img src="<%= image_url %>" alt="<%= user_badge.badge.name %>" class="certificate-image">
            <% else %>
              <!-- Fallback to icon if no certificate -->
              <i class="<%= user_badge.badge.icon_class rescue 'icon-star' %>"></i>
            <% end %>
          </div>
          <div class="badge-details">
            <div class="badge-header">
              <h4 class="badge-name">
                <svg viewBox="0 0 24 24" class="custom-badge-icon" xmlns="http://www.w3.org/2000/svg">
                  <path d="M5,16L3,5L8.5,12L12,6L15.5,12L21,5L19,16H5M12,18A2,2 0 0,1 14,20A2,2 0 0,1 12,22A2,2 0 0,1 10,20A2,2 0 0,1 12,18Z"/>
                </svg>
                <%=h user_badge.badge.name %>
              </h4>
              <% if can_edit_privacy %>
                <button class="privacy-toggle-btn <%= user_badge.is_public? ? 'public' : 'private' %>"
                        onclick="toggleBadgePrivacy(<%= user_badge.id %>, this)"
                        data-badge-id="<%= user_badge.id %>"
                        data-is-public="<%= user_badge.is_public? %>">
                  <span class="icon">
                    <% if user_badge.is_public? %>
                      <i class="icon-unlock"></i>
                    <% else %>
                      <i class="icon-lock"></i>
                    <% end %>
                  </span>
                  <span class="label">
                    <%= user_badge.is_public? ? 'Public' : 'Private' %>
                  </span>
                  <span class="spinner" style="display:none;">Saving...</span>
                </button>
                <span class="privacy-error" style="display:none;color:red;"></span>
              <% elsif !user_badge.is_public? %>
                <span class="privacy-indicator private">
                  <i class="icon-lock"></i> Private
                </span>
              <% end %>
            </div>
            <p class="badge-description"><%=h user_badge.badge.description %></p>
            <div class="badge-meta">
              <span class="earned-date">
                <i class="icon-clock"></i>
                Earned: <%=h user_badge.earned_at.strftime("%B %d, %Y") %>
              </span>
            </div>
          </div>
        </div>
      <% end %>
    </div>

  <% else %>
    <div class="empty-state no-badges">
      <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
      </svg>
      <% if @user == @current_user %>
        <h4>No Badges Yet</h4>
        <p>You haven't earned any badges yet. Keep learning and achieving to earn your first badge!</p>
        <p class="help-text">Click the button below to add your first badge.</p>
        <button class="add-badge-btn" onclick="showAddBadgeModal()">+ Add Badge</button>
      <% else %>
        <h4>No Public Badges</h4>
        <p>This user hasn't shared any badges publicly yet.</p>
      <% end %>
    </div>
  <% end %>

  <!-- Add Badge Modal -->
  <div class="modal-overlay" id="addBadgeModal">
    <div class="modal-container">
      <div class="modal-header">
        <h3 class="modal-title">+ Add Badge</h3>
        <button type="button" class="close-button" onclick="hideAddBadgeModal()">&times;</button>
      </div>
      
      <div class="modal-body">
        <form action="/profile/add_badge" method="post" enctype="multipart/form-data" id="addBadgeForm" class="badge-form">
          <div class="form-group">
            <label class="form-label" for="badge_title">Badge Title <span style="color: red;">*</span></label>
            <input type="text" name="title" id="badge_title" class="form-input" required>
          </div>
          
          <div class="form-group">
            <label class="form-label" for="badge_description">Description (Optional)</label>
            <textarea name="description" id="badge_description" class="form-textarea" placeholder="Enter badge description..." rows="3"></textarea>
          </div>
          
          <div class="form-group">
            <label class="form-label" for="badge_earned_at">Date earned <span style="color: red;">*</span></label>
            <input type="date" name="earned_at" id="badge_earned_at" class="form-input" required>
          </div>
          
          <div class="form-group">
            <label class="form-label">Certificate/Achievement <span style="color: red;">*</span></label>
            <div class="file-upload-container">
              <div class="file-upload-area">
                <input type="file" name="certificate" id="badge_certificate" class="file-input" accept="image/png,image/jpeg,image/jpg,image/webp" required>
                
                <!-- Upload Placeholder -->
                <div class="upload-placeholder">
                  <div class="upload-text">Click to upload or drag and drop</div>
                  <div class="upload-hint">PNG, JPG or WEBP(max 10MB)</div>
                </div>
                
                <!-- File Preview -->
                <div class="file-preview">
                  <img class="file-preview-image" style="display: none;" alt="Preview">
                  <div class="file-preview-icon" style="display: none;"></div>
                  <div class="file-preview-info">
                    <div class="file-preview-name"></div>
                    <div class="file-preview-size"></div>
                  </div>
                  <button type="button" class="remove-file-btn" title="Remove file">&times;</button>
                </div>
              </div>
            </div>
          </div>
          
          <div class="form-group">
            <label class="checkbox-label">
              <input type="checkbox" name="is_public" value="true" checked>
              Make badge/achievement public
            </label>
          </div>
          
          <div class="modal-footer">
            <button type="button" class="btn-cancel" onclick="hideAddBadgeModal()">Cancel</button>
            <button type="submit" class="btn-save">Save Badge</button>
          </div>
        </form>
      </div>
    </div>
  </div>

<script>
  function showTab(tabId) {
    // Hide all tab contents
    document.querySelectorAll('.tab-content').forEach(content => {
      content.classList.remove('active');
    });
    
    // Remove active class from all tab buttons
    document.querySelectorAll('.tab-button').forEach(button => {
      button.classList.remove('active');
    });
    
    // Show selected tab content
    document.getElementById(tabId).classList.add('active');
    
    // Add active class to clicked button
    event.target.closest('.tab-button').classList.add('active');
  }

  document.addEventListener('DOMContentLoaded', function() {
    // Add tooltips for grade information
    document.querySelectorAll('.letter-grade-cell').forEach(function(cell) {
      cell.addEventListener('mouseenter', function() {
        var courseCode = this.closest('tr').getAttribute('data-course-code');
        if (courseCode) {
          var tooltip = document.createElement('div');
          tooltip.className = 'grade-tooltip';
          tooltip.innerHTML = 'Grade for ' + courseCode;
          tooltip.style.cssText = 'position: absolute; background: #333; color: white; padding: 5px 10px; border-radius: 4px; font-size: 0.75rem; z-index: 1000; pointer-events: none;';
          
          document.body.appendChild(tooltip);
          
          var rect = this.getBoundingClientRect();
          tooltip.style.left = rect.left + 'px';
          tooltip.style.top = (rect.top - tooltip.offsetHeight - 5) + 'px';
          
          this.addEventListener('mouseleave', function() {
            if (tooltip.parentNode) {
              tooltip.parentNode.removeChild(tooltip);
            }
          }, { once: true });
        }
      });
    });
  });

  // Privacy toggle functionality with debugging
  function toggleBadgePrivacy(badgeId, button) {

    const isPublic = button.dataset.isPublic === 'true';
    const newState = !isPublic;

    // Show loading state
    button.disabled = true;
    button.innerHTML = '<i class="icon-spinner"></i> Updating...';
    
    // Check for CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]');
    
    
    fetch('/profile/toggle_badge_privacy', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': csrfToken ? csrfToken.getAttribute('content') : ''
      },
      body: JSON.stringify({
        user_badge_id: badgeId,
        is_public: newState
      })
    })
    .then(response => {
      return response.json();
    })
    .then(data => {

      if (data.success) {
        // Update button state
        button.dataset.isPublic = newState.toString();
        
        if (newState) {
          button.innerHTML = '<i class="icon-unlock"></i> Public';
          button.classList.remove('private');
          button.classList.add('public');
        } else {
          button.innerHTML = '<i class="icon-lock"></i> Private';
          button.classList.remove('public');
          button.classList.add('private');
        }
        
        // Update badge item styling
        const badgeItem = button.closest('.badge-item');
        if (newState) {
          badgeItem.classList.remove('private-badge');
        } else {
          badgeItem.classList.add('private-badge');
        }
        
        // Update privacy summary
        updatePrivacySummary();
        
      } else {
        alert('Failed to update privacy setting. Please try again.');
      }
    })
    .catch(error => {
      alert('An error occurred. Please try again.');
    })
    .finally(() => {
      button.disabled = false;
    });
  }

  function updatePrivacySummary() {
    // Recount badges and update summary
    const allBadges = document.querySelectorAll('.badge-item').length;
    const publicBadges = document.querySelectorAll('.privacy-toggle-btn[data-is-public="true"]').length;
    const privateBadges = allBadges - publicBadges;
    
    const summary = document.querySelector('.privacy-stats');
    if (summary) {
      summary.innerHTML = `
        <span class="stat-item">
          <strong>${allBadges}</strong> Total Badges
        </span>
        <span class="stat-item public">
          <i class="icon-unlock"></i> 
          <strong>${publicBadges}</strong> Public
        </span>
        <span class="stat-item private">
          <i class="icon-lock"></i> 
          <strong>${privateBadges}</strong> Private
        </span>
      `;
    }
  }

  // Modal control functions
  function showAddBadgeModal() {
    const modal = document.getElementById('addBadgeModal');
    if (modal) {
      modal.classList.add('active');
      document.body.style.overflow = 'hidden';
    }
  }

  function hideAddBadgeModal() {
    const modal = document.getElementById('addBadgeModal');
    if (modal) {
      modal.classList.remove('active');
      document.body.style.overflow = '';
      const form = document.getElementById('addBadgeForm');
      if (form) {
        form.reset();
        resetFileUpload();
      }
    }
  }

  // Close modal when clicking outside
  document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('addBadgeModal');
    if (modal) {
      modal.addEventListener('click', function(e) {
        if (e.target === this) {
          hideAddBadgeModal();
        }
      });
    }
  });

  // Enhanced File Upload Handler
  document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('badge_certificate');
    const uploadArea = document.querySelector('.file-upload-area');
    const uploadPlaceholder = document.querySelector('.upload-placeholder');
    const filePreview = document.querySelector('.file-preview');

    if (!fileInput || !uploadArea || !uploadPlaceholder || !filePreview) {
      return;
    }

    // Allow clicking the upload area to open file picker
    uploadArea.addEventListener('click', function () {
      fileInput.click();
    });

    // File select
    fileInput.addEventListener('change', function(e) {
      const file = e.target.files[0];
      if (file) {
        showFilePreview(file);
      } else {
        resetFileUpload();
      }
    });

    // Drag events
    uploadArea.addEventListener('dragover', function(e) {
      e.preventDefault();
      uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', function(e) {
      e.preventDefault();
      uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', function(e) {
      e.preventDefault();
      uploadArea.classList.remove('dragover');

      const files = e.dataTransfer.files;
      if (files.length > 0) {
        const file = files[0];
        if (isValidFile(file)) {
          fileInput.files = files;
          showFilePreview(file);
        }
      }
    });

    // Global handler for "remove file" button
    document.addEventListener('click', function(e) {
      if (e.target.classList.contains('remove-file-btn')) {
        e.preventDefault();
        resetFileUpload();
      }
    });

    function showFilePreview(file) {
      if (!isValidFile(file)) {
        alert('Only image files (PNG, JPG, JPEG, WEBP) under 10MB are allowed.');
        resetFileUpload();
        return;
      }

      const previewImage = filePreview.querySelector('.file-preview-image');
      const previewIcon = filePreview.querySelector('.file-preview-icon');
      const previewName = filePreview.querySelector('.file-preview-name');
      const previewSize = filePreview.querySelector('.file-preview-size');

      previewName.textContent = file.name;
      previewSize.textContent = formatFileSize(file.size);

      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = function(e) {
          previewImage.src = e.target.result;
          previewImage.style.display = 'block';
          previewIcon.style.display = 'none';
        };
        reader.readAsDataURL(file);
      } else {
        alert('Only image files are supported.');
        resetFileUpload();
        return;
      }

      uploadArea.classList.add('has-file');
      uploadPlaceholder.classList.add('hidden');
      filePreview.classList.add('active');
    }

    function resetFileUpload() {
      fileInput.value = '';

      const previewImage = filePreview.querySelector('.file-preview-image');
      const previewIcon = filePreview.querySelector('.file-preview-icon');

      if (previewImage) {
        previewImage.src = '';
        previewImage.style.display = 'none';
      }

      if (previewIcon) {
        previewIcon.style.display = 'none';
        previewIcon.innerHTML = '';
      }

      uploadArea.classList.remove('has-file');
      uploadPlaceholder.classList.remove('hidden');
      filePreview.classList.remove('active');
    }

    function isValidFile(file) {
      const validTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/webp'];
      const maxSize = 10 * 1024 * 1024; // 10MB
      return validTypes.includes(file.type) && file.size <= maxSize;
    }

    function formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes';
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Expose resetFileUpload globally
    window.resetFileUpload = resetFileUpload;
  });

  // Form submission
  document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('addBadgeForm');
    if (!form) return;

    form.addEventListener('submit', function(e) {
      e.preventDefault();

      const saveBtn = form.querySelector('.btn-save');
      const originalText = saveBtn.textContent;

      const title = form.querySelector('#badge_title').value.trim();
      const earnedAt = form.querySelector('#badge_earned_at').value;
      const certificate = form.querySelector('#badge_certificate').files[0];

      if (!title) return alert('Please enter a badge title');
      if (!earnedAt) return alert('Please select the date earned');
      if (!certificate) return alert('Please upload a certificate/achievement file');

      saveBtn.disabled = true;
      saveBtn.textContent = 'Saving...';

      const formData = new FormData(form);
      const csrfToken = document.querySelector('meta[name="csrf-token"]');

      fetch('/profile/add_badge', {
        method: 'POST',
        headers: {
          'X-CSRF-Token': csrfToken ? csrfToken.getAttribute('content') : ''
        },
        body: formData
      })
      .then(response => {
        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
        return response.json();
      })
      .then(data => {
        if (data.success) {
          alert('Badge added successfully!');
          hideAddBadgeModal();
          location.reload();
        } else {
          alert(data.error || 'Failed to save badge.');
        }
      })
      .catch(error => {
        alert('An error occurred while saving.');
      })
      .finally(() => {
        saveBtn.disabled = false;
        saveBtn.textContent = originalText;
      });
    });
  });

  // Legacy support
  function removeFile() {
    if (window.resetFileUpload) {
      window.resetFileUpload();
    }
  }
</script>

<% unless @read_only %>
  <div id="edit_user_details_mount_point"></div>
<% end %>

