<%
# Copyright (C) 2011 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.
%>

<% css_bundle :reports %>
<% js_bundle :teacher_activity_report %>
<% add_crumb(t('crumbs.interaction_report', "Student Interactions Report")) %>

<% provide :page_title, t('title', 'Teacher Activity Report') %>

<div class="teacher-activity-container">
  <div class="modern-header">
    <h1><%= t('headings.teacher_activity', "Teacher Activity Report for %{teacher}", :teacher => @teacher.name) %></h1>
    <p class="subtitle">Student engagement and performance overview</p>
  </div>

  <% @courses.each do |course, students| %>
    <div class="course-section">
      <div class="course-header">
        <h2 class="course-title"><%= course.name %></h2>
      </div>

      <% if students.blank? %>
        <div class="alert-section">
          <h3><%= t 'no_students', 'There are no students to report on.' %></h3>
        </div>
      <% else %>
        <% # Collect at-risk students for alert section %>
        <% at_risk_students = students.select do |student|
             last_access = student[:access_report] && student[:access_report][:last_access]
             last_access.nil? || last_access < 7.days.ago
           end %>

        <% if at_risk_students.any? %>
          <div class="alert-section">
            <h3>⚠️ Students Requiring Attention</h3>
            <div class="alert-list">
              <% at_risk_students.each do |student| %>
                <div class="alert-item">
                  <%= student[:enrollment].user.name %> - No activity >7d
                </div>
              <% end %>
            </div>
          </div>
        <% end %>

        <div class="students-grid">
          <% students.each do |student| %>
            <% flags = [] %>
            <% # Calculate flags %>
            <% last_access = student[:access_report] && student[:access_report][:last_access] %>
            <% if last_access.nil? || last_access < 7.days.ago %>
              <% flags << { text: "No activity >7d", type: "critical" } %>
            <% end %>
            <% avg_views = students.map { |s| s[:access_report]&.[](:total_views).to_i }.sum / students.size.to_f %>
            <% if student[:access_report] && student[:access_report][:total_views].to_i < (avg_views * 0.5) %>
              <% flags << { text: "Low interaction", type: "warning" } %>
            <% end %>
            <% ar = student[:access_report] %>
            <% if ar && ar[:views_last_7_days] && ar[:views_prev_7_days] %>
              <% prev = ar[:views_prev_7_days].to_i; curr = ar[:views_last_7_days].to_i %>
              <% if prev > 0 && curr < (prev * 0.3) %>
                <% flags << { text: "Sudden drop", type: "warning" } %>
              <% end %>
            <% end %>
            <% if flags.empty? %>
              <% flags << { text: "Active", type: "good" } %>
            <% end %>

            <div class="student-card">
              <div class="student-header">
                <%= link_to(student[:enrollment].user.name, course_user_url(course, student[:enrollment].user_id), class: "student-name") %>
                <div class="student-actions">
                  <% if course.user_has_been_instructor?(@current_user) %>
                    <%= link_to message_user_path(student[:enrollment].user, course), class: "message_student_link", title: t('message_student', 'Message this student') do %>
                      📧
                    <% end %>
                  <% end %>
                  <a href="<%= context_url(course, :context_user_usage_url, student[:enrollment].user) %>" 
                     class="action-btn" title="View Full Report">
                    📊
                  </a>
                </div>
              </div>
              
              <div class="student-metrics">
                <div class="metric">
                  <div class="metric-value"><%= n(student[:enrollment].computed_current_score, percentage: true, precision: 1) %></div>
                  <div class="metric-label">Current Score</div>
                  <div class="progress-bar">
                    <div class="progress-fill" style="width: <%= student[:enrollment].computed_current_score || 0 %>%"></div>
                  </div>
                </div>
                <div class="metric">
                  <div class="metric-value"><%= n(student[:enrollment].computed_final_score, percentage: true, precision: 1) %></div>
                  <div class="metric-label">Final Score</div>
                  <div class="progress-bar">
                    <div class="progress-fill" style="width: <%= student[:enrollment].computed_final_score || 0 %>%"></div>
                  </div>
                </div>
              </div>

              <% if student[:access_report] %>
                <div class="access-summary">
                  <div class="access-stats">
                    <div class="stat">
                      <span class="label">Views</span>
                      <span class="value"><%= student[:access_report][:total_views] %></span>
                    </div>
                    <div class="stat">
                      <span class="label">Participations</span>
                      <span class="value"><%= student[:access_report][:total_participations] %></span>
                    </div>
                    <div class="stat">
                      <span class="label">Items</span>
                      <span class="value"><%= student[:access_report][:unique_items_accessed] %></span>
                    </div>
                  </div>
                  <div class="last-interaction">
                    <% if student[:last_interaction] %>
                      Last interaction: <%= t 'last_time', { :zero => "less than 1 day ago", :one => "1 day ago", :other => "%{count} days ago" }, :count => (((Time.now - student[:last_interaction])/60)/1440).abs.to_i %>
                    <% else %>
                      Last interaction: <%= t 'last_time_never', 'never' %>
                    <% end %>
                    <% if student[:access_report][:last_access] %>
                      • Last access: <%= time_ago_in_words(student[:access_report][:last_access]) %> ago
                    <% end %>
                  </div>
                  <a href="<%= context_url(course, :context_user_usage_url, student[:enrollment].user) %>" 
                     class="access-link">View Full Report</a>
                </div>
              <% else %>
                <div class="access-summary">
                  <span style="color: #666; font-style: italic;">No activity data available</span>
                </div>
              <% end %>

              <% if student[:ungraded].any? %>
                <div class="ungraded-assignments">
                  <div class="ungraded-title">📝 Ungraded Assignments (<%= student[:ungraded].size %>)</div>
                  <% student[:ungraded].each do |submission| %>
                    <%= link_to(submission.assignment.title, speed_grader_course_gradebook_path(course, assignment_id: submission.assignment.id, student_id: student[:enrollment].user_id), class: "assignment-link") %>
                    <% if submission.submitted_at %>
                      <div class="assignment-date">
                        <%= t 'submitted_time', { :zero => "submitted less than 1 day ago", :one => "submitted 1 day ago", :other => "submitted %{count} days ago" }, :count => (((Time.now - submission.submitted_at)/60)/1440).abs.to_i %>
                      </div>
                    <% end %>
                  <% end %>
                </div>
              <% end %>

              <div class="student-flags">
                <% flags.each do |flag| %>
                  <span class="flag <%= flag[:type] %>"><%= flag[:text] %></span>
                <% end %>
              </div>
            </div>
          <% end %>
        </div>

        <!-- Enhanced Chart Section -->
        <div class="charts-section">
          <div class="charts-header">
            <h3>📊 Student Activity Analytics</h3>
            <p>Comprehensive overview of student engagement patterns and learning behaviors</p>
          </div>
          
          <!-- Activity Summary Cards -->
          <div class="tab-content" id="activityTabContent-<%= course.id %>">
            <!-- Views Tab -->
            <div class="tab-pane fade show active" id="views-<%= course.id %>" role="tabpanel">
              <div class="metric-description">
                <h4>📖 What are Page Views?</h4>
                <p>Page views represent each time a student accesses course content including assignment pages, discussion posts, module items, announcements, and other course materials. Higher view counts typically indicate more engaged students who actively browse and revisit course content.</p>
              </div>
              
              <div class="chart-container">
                <canvas 
                  class="course-activity-chart"
                  id="course-views-chart-<%= course.id %>"
                  width="600" height="250"
                  data-labels='<%= students.map { |s| s[:enrollment].user.name }.to_json %>'
                  data-data='<%= students.map { |s| s[:access_report] ? s[:access_report][:total_views] : 0 }.to_json %>'
                  data-label="Views"
                  data-color="#2d5b3f"
                ></canvas>
              </div>
              
              <% 
                view_data = students.map { |s| s[:access_report] ? s[:access_report][:total_views].to_i : 0 }
                avg_views = view_data.sum / students.size.to_f
                max_views = view_data.max
                low_activity_count = view_data.count { |v| v < 50 }
              %>
              
              <div class="metric-stats">
                <div class="metric-stat">
                  <span class="stat-value"><%= avg_views.round %></span>
                  <span class="stat-label">Average Views</span>
                </div>
                <div class="metric-stat">
                  <span class="stat-value"><%= max_views %></span>
                  <span class="stat-label">Highest Views</span>
                </div>
                <div class="metric-stat">
                  <span class="stat-value"><%= low_activity_count %></span>
                  <span class="stat-label">Students < 50 Views</span>
                </div>
              </div>
              
            </div>
            
            <!-- Participations Tab -->
            <div class="tab-pane fade" id="participations-<%= course.id %>" role="tabpanel">
              <div class="metric-description">
                <h4>🗣️ What are Participations?</h4>
                <p>Participations count active student contributions including discussion posts, replies, quiz submissions, assignment uploads, and other interactive activities. This metric measures actual engagement rather than passive consumption of content.</p>
              </div>
              
              <div class="chart-container">
                <canvas 
                  class="course-activity-chart"
                  id="course-participations-chart-<%= course.id %>"
                  width="600" height="250"
                  data-labels='<%= students.map { |s| s[:enrollment].user.name }.to_json %>'
                  data-data='<%= students.map { |s| s[:access_report] ? s[:access_report][:total_participations] : 0 }.to_json %>'
                  data-label="Participations"
                  data-color="#f4a534"
                ></canvas>
              </div>
              
              <% 
                participation_data = students.map { |s| s[:access_report] ? s[:access_report][:total_participations].to_i : 0 }
                avg_participations = participation_data.sum / students.size.to_f
                max_participations = participation_data.max
                no_participation_count = participation_data.count { |p| p == 0 }
              %>
              
              <div class="metric-stats">
                <div class="metric-stat">
                  <span class="stat-value"><%= avg_participations.round(1) %></span>
                  <span class="stat-label">Average Participations</span>
                </div>
                <div class="metric-stat">
                  <span class="stat-value"><%= max_participations %></span>
                  <span class="stat-label">Most Active Student</span>
                </div>
                <div class="metric-stat">
                  <span class="stat-value"><%= no_participation_count %></span>
                  <span class="stat-label">Students with 0 Participations</span>
                </div>
              </div>
              
            </div>
            
            <!-- Items Tab -->
            <div class="tab-pane fade" id="items-<%= course.id %>" role="tabpanel">
              <div class="metric-description">
                <h4>📚 What are Content Items?</h4>
                <p>Content items represent unique course materials accessed by students including assignments, files, videos, external links, pages, and quizzes. This metric shows content breadth - how much of the available course material each student has explored.</p>
              </div>
              
              <div class="chart-container">
                <canvas 
                  class="course-activity-chart"
                  id="course-items-chart-<%= course.id %>"
                  width="600" height="250"
                  data-labels='<%= students.map { |s| s[:enrollment].user.name }.to_json %>'
                  data-data='<%= students.map { |s| s[:access_report] ? s[:access_report][:unique_items_accessed] : 0 }.to_json %>'
                  data-label="Items"
                  data-color="#3d6b4f"
                ></canvas>
              </div>
              
              <% 
                items_data = students.map { |s| s[:access_report] ? s[:access_report][:unique_items_accessed].to_i : 0 }
                avg_items = items_data.sum / students.size.to_f
                max_items = items_data.max
                # Estimate total available items (you may want to get this from the course data)
                total_available_items = max_items > 0 ? [max_items + 5, 20].min : 18
                completion_rate = max_items > 0 ? ((avg_items / total_available_items) * 100).round : 0
              %>
              
              <div class="metric-stats">
                <div class="metric-stat">
                  <span class="stat-value"><%= avg_items.round(1) %></span>
                  <span class="stat-label">Average Items</span>
                </div>
                <div class="metric-stat">
                  <span class="stat-value"><%= total_available_items %></span>
                  <span class="stat-label">Estimated Available</span>
                </div>
                <div class="metric-stat">
                  <span class="stat-value"><%= completion_rate %>%</span>
                  <span class="stat-label">Average Completion</span>
                </div>
              </div>
              
            </div>
          </div>
        </div>
      <% end %>
      
      <% if params[:student_id] %>
        <div style="margin-top: 1.5rem; text-align: center;">
          <%= link_to(t('links.view_full_course', 'View the full Student Interaction Report for %{course}', :course => course.name), user_course_teacher_activity_url(@teacher, course), :class => 'access-link', style: 'font-size: 1rem;') %>
        </div>
      <% end %>
    </div>
  <% end %>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
  document.addEventListener("DOMContentLoaded", function() {
    // Enhanced chart options with tooltips
    const chartOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: { 
        legend: { display: false },
        tooltip: {
          callbacks: {
            afterLabel: function(context) {
              const datasetLabel = context.dataset.label;
              const value = context.parsed.y;
              
              if (datasetLabel === 'Views') {
                return value > 100 ? 'High engagement' : value > 50 ? 'Moderate engagement' : 'Low engagement';
              } else if (datasetLabel === 'Participations') {
                return value > 10 ? 'Very active' : value > 5 ? 'Active' : value > 0 ? 'Limited activity' : 'No participation';
              } else if (datasetLabel === 'Items') {
                const percentage = Math.round((value / 18) * 100);
                return `${percentage}% of available content`;
              }
            }
          }
        }
      },
      scales: { 
        y: { 
          beginAtZero: true, 
          ticks: { 
            precision: 0,
            color: '#666'
          },
          grid: { color: '#e0e0e0' }
        },
        x: {
          grid: { display: false },
          ticks: { 
            color: '#666',
            maxRotation: 45
          }
        }
      },
      animation: {
        duration: 800,
        easing: 'easeInOutQuart'
      }
    };

    // Initialize charts with enhanced styling
    document.querySelectorAll('.course-activity-chart').forEach(function(canvas) {
      const labels = JSON.parse(canvas.dataset.labels);
      const data = JSON.parse(canvas.dataset.data);
      const label = canvas.dataset.label;
      const color = canvas.dataset.color;
      
      // Create gradient
      const ctx = canvas.getContext('2d');
      const gradient = ctx.createLinearGradient(0, 0, 0, 250);
      gradient.addColorStop(0, color);
      gradient.addColorStop(1, color + '80');
      
      new Chart(ctx, {
        type: 'bar',
        data: {
          labels: labels,
          datasets: [{
            label: label,
            data: data,
            backgroundColor: gradient,
            borderColor: color,
            borderWidth: 2,
            borderRadius: 8,
            borderSkipped: false,
          }]
        },
        options: chartOptions
      });
    });
  });
</script>
