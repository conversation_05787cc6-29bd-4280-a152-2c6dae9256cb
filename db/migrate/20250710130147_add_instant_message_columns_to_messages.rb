class AddInstantMessageColumnsToMessages < ActiveRecord::Migration[7.1]
  tag :predeploy

  def change
    add_column :messages, :delivered_at, :datetime
    add_column :messages, :seen_at, :datetime
    add_column :messages, :message_type, :string, default: 'notification'
    add_column :messages, :recipient_id, :bigint

    # Add indexes for better query performance
    add_index :messages, :delivered_at
    add_index :messages, :seen_at
    add_index :messages, :message_type
    add_index :messages, :recipient_id
    add_index :messages, [:user_id, :recipient_id], name: 'index_messages_on_user_and_recipient'
  end
end
