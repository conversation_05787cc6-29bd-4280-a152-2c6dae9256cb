class CreateUserOnlineStatuses < ActiveRecord::Migration[7.1]
  tag :predeploy

  def change
    create_table :user_online_statuses do |t|
      t.references :user, null: false, foreign_key: true, index: { unique: true }
      t.datetime :last_seen_at, null: false
      t.boolean :online, default: false, null: false

      t.timestamps
    end

    add_index :user_online_statuses, :online
    add_index :user_online_statuses, :last_seen_at
  end
end
