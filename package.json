{"name": "canvas-lms", "license": "AGPL-3.0", "version": "0.0.0", "engines": {"node": ">=20.0.0", "yarn": "^1.19.1"}, "private": true, "workspaces": {"packages": ["gems/plugins/*", "packages/*", "ui/engine", "ui/shared/*"]}, "dependencies": {"@apollo/client": "3.12.4", "@craftjs/core": "0.2.11", "@hookform/resolvers": "^3.9.0", "@instructure/brandable_css": "^3", "@instructure/canvas-media": "*", "@instructure/debounce": "9.10.1", "@instructure/emotion": "9.10.1", "@instructure/media-capture": "^9.0.0", "@instructure/media-capture-new": "npm:@instructure/media-capture@10.2.1-snapshot-543", "@instructure/outcomes-ui": "^3", "@instructure/react-crop": "^5.0.1", "@instructure/reactour": "https://github.com/instructure/reactour#b908434fe544703e26bc67c67c4111252c401f92", "@instructure/ready": "*", "@instructure/studio-player": "0.4.0", "@instructure/ui-a11y-content": "9.10.1", "@instructure/ui-a11y-utils": "9.10.1", "@instructure/ui-alerts": "9.10.1", "@instructure/ui-avatar": "9.10.1", "@instructure/ui-badge": "9.10.1", "@instructure/ui-billboard": "9.10.1", "@instructure/ui-breadcrumb": "9.10.1", "@instructure/ui-buttons": "9.10.1", "@instructure/ui-calendar": "9.10.1", "@instructure/ui-checkbox": "9.10.1", "@instructure/ui-color-picker": "9.10.1", "@instructure/ui-date-input": "9.10.1", "@instructure/ui-date-time-input": "9.10.1", "@instructure/ui-decorator": "9.10.1", "@instructure/ui-dialog": "9.10.1", "@instructure/ui-drawer-layout": "9.10.1", "@instructure/ui-editable": "9.10.1", "@instructure/ui-file-drop": "9.10.1", "@instructure/ui-flex": "9.10.1", "@instructure/ui-focusable": "9.10.1", "@instructure/ui-form-field": "9.10.1", "@instructure/ui-grid": "9.10.1", "@instructure/ui-heading": "9.10.1", "@instructure/ui-i18n": "9.10.1", "@instructure/ui-icons": "9.10.1", "@instructure/ui-img": "9.10.1", "@instructure/ui-link": "9.10.1", "@instructure/ui-list": "9.10.1", "@instructure/ui-media-player": "^9.0.0", "@instructure/ui-menu": "9.10.1", "@instructure/ui-metric": "9.10.1", "@instructure/ui-modal": "9.10.1", "@instructure/ui-motion": "9.10.1", "@instructure/ui-navigation": "9.10.1", "@instructure/ui-number-input": "9.10.1", "@instructure/ui-overlays": "9.10.1", "@instructure/ui-pagination": "9.10.1", "@instructure/ui-pill": "9.10.1", "@instructure/ui-portal": "9.10.1", "@instructure/ui-progress": "9.10.1", "@instructure/ui-radio-input": "9.10.1", "@instructure/ui-rating": "9.10.1", "@instructure/ui-react-utils": "9.10.1", "@instructure/ui-responsive": "9.10.1", "@instructure/ui-select": "9.10.1", "@instructure/ui-side-nav-bar": "9.10.1", "@instructure/ui-simple-select": "9.10.1", "@instructure/ui-spinner": "9.10.1", "@instructure/ui-svg-images": "9.10.1", "@instructure/ui-table": "9.10.1", "@instructure/ui-tabs": "9.10.1", "@instructure/ui-tag": "9.10.1", "@instructure/ui-text": "9.10.1", "@instructure/ui-text-area": "9.10.1", "@instructure/ui-text-input": "9.10.1", "@instructure/ui-themes": "9.10.1", "@instructure/ui-time-select": "9.10.1", "@instructure/ui-toggle-details": "9.10.1", "@instructure/ui-tooltip": "9.10.1", "@instructure/ui-top-nav-bar": "9.10.1", "@instructure/ui-tray": "9.10.1", "@instructure/ui-tree-browser": "9.10.1", "@instructure/ui-view": "9.10.1", "@instructure/uid": "9.10.1", "@instructure/updown": "^1.3", "@microsoft/immersive-reader-sdk": "^1.1.0", "@ns0m/hermite-resize": "^2.2.10", "@peculiar/asn1-schema": "^2.3.6", "@peculiar/x509": "^1.9.3", "@popperjs/core": "^2.9.2", "@rails/actioncable": "^8.0.200", "@rspack/cli": "^0.7.5", "@rspack/core": "^0.7.5", "@sentry/cli": "^2.21.5", "@sentry/react": "^7.81.0", "@swc/core": "1.5.0", "@swc/helpers": "^0.5.6", "@swc/jest": "^0.2.36", "@tanstack/query-sync-storage-persister": "^4.36.1", "@tanstack/react-query": "^4.36.1", "@tanstack/react-query-persist-client": "^4.36.1", "@types/b-spline": "^2.0.4", "@types/dagre": "^0.7.52", "@types/jquery.cookie": "^1.4.32", "@types/react-dnd": "2.0.36", "@types/twitter-text": "^3.1.2", "@types/uuid": "^9.0.0", "@vitest/coverage-v8": "^3.0.3", "apollo-utilities": "^1.3.2", "apollo3-cache-persist": "^0.7.0", "axios": "^0.28.0", "axios-cache-adapter": "^2.7.0", "b-spline": "^2.0.2", "backbone": "1.1.1", "big.js": "^6.2.1", "browserslist": "^4.22.2", "canvas_offline_course_viewer": "https://github.com/instructure/canvas_offline_course_viewer.git#1.2.0", "chart.js": "^3.9.1", "chartjs-adapter-moment": "^1.0.0", "classnames": "2.5.1", "color-slicer": "0.8.0", "coverage-istanbul-loader": "^3.0.5", "create-react-class": "^15.6.3", "cross-fetch": "^4.1.0", "crypto-js": "^4.1.1", "d3": "3.5.17", "dagre": "^0.8.5", "emoji-mart": "^3.0.1", "export-from-json": "^1.6.0", "final-form": "^4.20.2", "formdata-polyfill": "^3.0.9", "fullcalendar": "3.10.5", "graphiql": "1.7.2", "graphiql-explorer": "^0.4.2", "graphql": "^16", "graphql-request": "^6.1.0", "html2canvas": "^1.4.1", "i18n-js": "^3", "ic-ajax": "~2.0.1", "ic-tabs": "0.1.3", "immer": "^3", "immutability-helper": "^3", "immutable": "^3.8.2", "invariant": "^2.2.2", "is-valid-domain": "^0.0.11", "jquery-migrate": "3.4.1", "jquery-ui-touch-punch": "^0.2.3", "jquery.cookie": "^1.4.1", "jquery.elastic": "1.0.0", "jquery3": "npm:j<PERSON>y@^3.7.1", "localforage": "^1.10.0", "location-origin": "^1.1.4", "lodash": "^4.17.21", "md5": "^2.2.1", "mediaelement": "https://github.com/instructure/mediaelement.git#master", "mime": "^4.0.1", "minimatch": "~3.0.4", "moment": "^2.29.4", "moment-timezone": "^0.5.45", "nanoid": "3.3.8", "normalize-scroll-left": "^0.2", "page": "^1.11", "parse-decimal-number": "1.0.0", "parse-link-header": "^1", "prop-types": "^15", "punycode": "2.3.1", "qs": "^6.6.0", "re-resizable": "6.9.16", "react": "^18", "react-beautiful-dnd": "^13.1.1", "react-confetti-explosion": "^2.1.2", "react-contenteditable": "^3.3.7", "react-dnd": "6.0", "react-dnd-html5-backend": "6.0", "react-dom": "^18", "react-final-form": "^6.5.3", "react-hook-form": "^7.53.0", "react-identicons": "^1.2.5", "react-immutable-proptypes": "^2.1.0", "react-lazy-load": "^4.0.1", "react-modal": "^3", "react-moveable": "0.56.0", "react-popper": "^2.2.5", "react-redux": "^5.1.1", "react-router-dom": "^6.16.0", "react-slick": "^0.30.2", "react-tokeninput": "^2.5.0", "react-transition-group": "^1", "react-use": "17.6.0", "redux": "^4.0.1", "redux-actions": "^2.6.4", "redux-promise": "^0.6", "redux-saga": "^0.16.0", "redux-thunk": "^3.1.0", "reselect": "^4.0.0", "rspack-manifest-plugin": "^5.0.0", "slick-carousel": "^1.8.1", "spin.js": "2.3.2", "styled-components": ">= 4", "swc-loader": "^0.2.6", "swc-plugin-coverage-instrument": "^0.0.21", "swfobject": "^2.2.1", "tablesorter": "2.32.0", "timezone": "npm:@instructure/timezone@1.0.30", "tinycolor2": "^1.6.0", "twitter-text": "^3.1.0", "underscore": "^1.13.6", "use-debounce": "^3", "use-media-set": "^1.1", "uuid": "3.4.0", "velocity-animate": "^1.5.0", "webpack-retry-chunk-load-plugin": "^3.1.1", "yarn-deduplicate": "^3.0.1", "zod": "^3.22.4", "zustand": "^4.5.5"}, "devDependencies": {"@babel/cli": "^7.0.0", "@babel/core": "^7.0.0", "@babel/parser": "^7", "@babel/plugin-proposal-class-properties": "^7", "@babel/plugin-proposal-optional-chaining": "^7", "@babel/plugin-transform-modules-commonjs": "^7", "@babel/plugin-transform-react-constant-elements": "^7", "@babel/plugin-transform-react-inline-elements": "^7", "@babel/plugin-transform-runtime": "^7", "@babel/preset-env": "^7", "@babel/preset-react": "^7", "@babel/preset-typescript": "^7", "@biomejs/biome": "1.9.4", "@eslint-community/eslint-plugin-eslint-comments": "^4.4.1", "@eslint/js": "^9.25.1", "@graphql-tools/mock": "^9", "@graphql-tools/schema": "^9", "@instructure/browserslist-config-canvas-lms": ">=2", "@instructure/i18nliner": "^3", "@instructure/i18nliner-canvas": "^1.3", "@instructure/i18nliner-handlebars": "^2", "@instructure/i18nliner-runtime": "^1.0.2", "@rspack/plugin-react-refresh": "^0.7.5", "@sheerun/mutationobserver-shim": "0.3.2", "@testing-library/dom": "^8", "@testing-library/jest-dom": "^5", "@testing-library/react": "^12", "@testing-library/react-hooks": "^8", "@testing-library/user-event": "^14", "@types/big.js": "^6.2.2", "@types/enzyme": "^3.10.16", "@types/enzyme-adapter-react-16": "^1.0.9", "@types/jquery": "^3.5.6", "@types/jqueryui": "^1.12.16", "@types/lodash": "^4.14.72", "@types/moxios": "^0.4.17", "@types/parse-link-header": "^1.0.0", "@types/qs": "^6.9.8", "@types/react": "^18", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18", "@types/react-slick": "^0.23.13", "@types/sinon": "^17.0.3", "@types/tinycolor2": "^1.4.6", "@types/rails__actioncable": "^6.1.11", "@vitest/ui": "^3.0.3", "@yarnpkg/lockfile": "^1.0.2", "array-flat-polyfill": "^1.0.1", "axe-core": "~2.1.7", "axios-mock-adapter": "^2.1.0", "babel-jest": "^29.7.0", "babel-loader": "^9.1.3", "babel-plugin-transform-react-remove-prop-types": "^0.4", "babel-plugin-typescript-to-proptypes": "^2.1.0", "chai-assert-change": "^2.0.0", "change-case": "3.0.1", "concurrently": "^4", "core-js": "^3.20.3", "core-js-builder": "^3", "css-loader": "^3", "dependency-cruiser": "^16.9.0", "enzyme": "^3", "enzyme-adapter-react-16": "^1.15.7", "enzyme-to-json": "^3.3.4", "eslint": "^9.25.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest": "^28.9.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-lodash": "^8.0.0", "eslint-plugin-notice": "^1.0.0", "eslint-plugin-promise": "^7.2.1", "eslint-plugin-qunit": "^8.1.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-compiler": "^19.0.0-beta-27714ef-20250124", "eslint-plugin-react-hooks": "^5.1.0", "exports-loader": "^0.7", "fast-glob": "^3.2.4", "fetch-mock": "9.11.0", "file-loader": "^4", "find-up": "^5.0.0", "gglobby": "0.0.3", "glob": "^7", "globals": "^15.15.0", "gulp": "^4", "gulp-file": "^0.4", "gulp-filter": "^6", "gulp-insert": "^0.5", "gulp-load-plugins": "^2", "gulp-rename": "^2", "gulp-rev": "^9", "gulp-sourcemaps": "^2", "gulp-uglify": "^3", "handlebars": "1.3.0", "happy-dom": "^12.10.3", "imports-loader": "^0.8", "istanbul-merge": "^1.1.1", "jest": "^29.7.0", "jest-canvas-mock": "^2", "jest-config": "^28", "jest-environment-jsdom": "^29.7.0", "jest-fetch-mock": "^3.0.3", "jest-fixed-jsdom": "^0.0.9", "jest-junit": "^7", "jest-localstorage-mock": "^2", "jest-moxios-utils": "^1", "jsdom": "25.0.1", "jsdom-global": "^3.0.2", "json-loader": "^0.5.7", "lint-staged": "^9", "loader-utils": "^1", "merge-stream": "^2", "micromatch": "^4.0.4", "mkdirp": "^1.0.4", "mockdate": "^2.0.2", "moment-timezone-data-webpack-plugin": "^1.5.1", "moxios": "^0.4", "msw": "^2.0.12", "nyc": "^13", "patch-package": "^8.0.0", "qunitjs": "^1.23.0", "react-moment-proptypes": "^1.4.0", "react-test-renderer": "^18", "redux-devtools-extension": "^2.13.2", "redux-logger": "^3.0.6", "sass-direction": "^1", "script-loader": "^0.7", "sinon": "9.2.4", "stream-browserify": "^3", "style-loader": "^0.23", "stylelint": "10.1.0", "terser-webpack-plugin": "5.3.10", "through2": "^3", "timezone-mock": "^1.3.1", "tinymce": "^5", "ts-node": "^10.9.2", "typescript": "^5.7.2", "typescript-eslint": "^8.18.0", "vitest": "^3.0.3", "waait": "^1", "webpack": "^5", "webpack-bundle-analyzer": "^4.5.0", "webpack-manifest-plugin": "^5", "wrap-ansi": "^7.0.0", "wsrun": "^5", "xsslint": "instructure/xsslint#babel7", "yaml-loader": "^0.5"}, "optionalDependencies": {"@storybook/addon-actions": "^6.5.9", "@storybook/addon-essentials": "^6.5.9", "@storybook/addon-links": "^6.5.9", "@storybook/addon-storyshots": "^6.5.9", "@storybook/builder-webpack5": "^6.5.9", "@storybook/manager-webpack5": "^6.5.9", "@storybook/react": "^6.5.9"}, "browserslist": ["extends @instructure/browserslist-config-canvas-lms"], "repository": "instructure/canvas-lms", "scripts": {"test": "jest --color", "test:coverage": "script/generate_js_coverage", "test:watch": "jest --watch .", "test:vitest": "vitest run --color", "test:vitest:watch": "vitest watch --color", "test:vitest:coverage": "vitest run --color --coverage", "test:jest": "jest --color", "test:jest:coverage": "NODE_OPTIONS=\"--openssl-legacy-provider --max_old_space_size=5120\" jest --color --coverage --testTimeout=30000", "test:jest:debug": "node --inspect-brk=0.0.0.0 ./node_modules/jest/bin/jest.js", "test:jest:watch": "jest --color --watch", "test:jest:build": "if [ \"$COVERAGE\" = \"1\" ]; then yarn test:jest:coverage --maxWorkers=5 --shard=$CI_NODE_INDEX/$CI_NODE_TOTAL; else yarn test:jest --maxWorkers=5 --shard=$CI_NODE_INDEX/$CI_NODE_TOTAL; fi", "test:packages": "wsrun --report -m -l -s -c test", "build": "yarn run build:css && yarn run build:packages && yarn run build:js", "build:watch": "concurrently --raw \"yarn build:css:watch\" \"yarn build:js:watch\"", "build:css": "brandable_css", "build:css:compressed": "SASS_STYLE=compressed brandable_css", "build:css:watch": "brandable_css --watch", "build:js": "yarn run webpack-development", "build:js:watch": "yarn run webpack", "build:packages": "wsrun --fast-exit --exclude-missing --report -c build", "check": "yarn check:ts && yarn lint --quiet", "check:js": "tsc --checkJs -p tsconfig.json", "check:ts": "tsc -p tsconfig.json", "check:ts:watch": "tsc --watch -p tsconfig.json", "serve": "gulp rev 1> /dev/null & NODE_ENV=development rspack serve", "lint:packages": "eslint packages --cache", "lint:staged": "lint-staged", "lint:style": "stylelint './app/**/*.{css,scss}' './packages/**/*.{css,scss}'", "lint:xss": "node ./script/xsslint.js", "i18n:check": "node_modules/@instructure/i18nliner-canvas/bin/i18nliner check", "i18n:extract": "node_modules/@instructure/i18nliner-canvas/bin/i18nliner export --translationsFile config/locales/generated/en-js.json --indexFile config/locales/generated/en-js-index.json", "lint": "eslint ui --cache", "postinstall": "yarn dedupe-yarn; patch-package; [ -f ./script/install_hooks ] && ./script/install_hooks || true", "webpack:analyze": "SKIP_SOURCEMAPS=1 WEBPACK_PEDANTIC=0 rspack --analyze", "webpack": "gulp rev 1> /dev/null & NODE_ENV=development rspack --watch=true", "webpack-development": "NODE_ENV=development rspack build", "webpack-production": "NODE_ENV=production rspack build", "jspec": "./spec/jspec.sh", "jspec-watch": "./spec/jspec.sh --watch", "a11y-report": "./spec/jspec.sh --a11y", "upgrade-and-dedupe": "rm -rf yarn.lock node_modules && yes 1 | yarn install --flat --production --ignore-scripts && git checkout package.json && yarn install && git add yarn.lock", "upgrade-instructure-ui": "script/upgrade-instructure-ui", "dedupe-yarn": "yarn yarn-deduplicate", "clean": "wsrun --serial --exclude-missing -c clean", "storybook": "start-storybook -p 6006 --no-dll --static-dir ./public/javascripts,./public", "test-storybook": "yarn jest --roots .storybook --testMatch **/storybook.test.js"}, "resolutions": {"graphael": "https://github.com/instructure/graphael.git", "@storybook/react-docgen-typescript-plugin": "1.0.6--canary.9.cd77847.0", "@instructure/emotion": "9.10.1", "@instructure/ui-a11y-content": "9.10.1", "@instructure/ui-alerts": "9.10.1", "@instructure/ui-buttons": "9.10.1", "@instructure/ui-color-utils": "9.10.1", "@instructure/ui-dom-utils": "9.10.1", "@instructure/ui-drilldown": "9.10.1", "@instructure/ui-grid": "9.10.1", "@instructure/ui-icons": "9.10.1", "@instructure/ui-menu": "9.10.1", "@instructure/ui-responsive": "9.10.1", "@instructure/ui-simple-select": "9.10.1", "@instructure/ui-spinner": "9.10.1", "@instructure/ui-text": "9.10.1", "@instructure/ui-text-input": "9.10.1", "@instructure/ui-truncate-text": "9.10.1", "@instructure/ui-view": "9.10.1", "@instructure/uid": "9.10.1", "jsdom": "25.0.1"}, "packageManager": "yarn@1.19.1+sha512.8019df6cbf6b618d391add1c8c986cfec8aa4171d89596a54e32b79d79f640edb4c5b90814fa1bf8b947e3830be3b19c478554f7fd9d61c93505614cd096afc7"}