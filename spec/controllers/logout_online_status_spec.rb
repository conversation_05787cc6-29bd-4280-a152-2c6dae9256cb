require 'spec_helper'

describe ApplicationController, type: :controller do
  controller do
    def test_logout
      logout_current_user
      render json: { success: true }
    end
  end

  let(:user) { user_model }

  before do
    routes.draw { post 'test_logout' => 'anonymous#test_logout' }
    user_session(user)
    # Mark user as online initially
    UserOnlineStatus.update_status(user.id, online: true)
  end

  describe '#logout_current_user' do
    it 'marks user as offline when logging out' do
      expect(UserOnlineStatus.online?(user.id)).to be true
      
      post :test_logout
      
      expect(UserOnlineStatus.online?(user.id)).to be false
    end

    it 'broadcasts offline status via ActionCable when logging out' do
      expect(ActionCable.server).to receive(:broadcast).with(
        'presence_channel',
        { user_id: user.id, online: false }
      )
      
      post :test_logout
    end

    it 'stamps logout time' do
      expect(user).to receive(:stamp_logout_time!)
      
      post :test_logout
    end
  end
end
