require 'spec_helper'

describe UnifiedMessagesController do
  before :once do
    course_with_teacher(active_all: true)
    student_in_course(active_all: true)
  end

  before :each do
    user_session(@teacher)
  end

  describe "GET #index" do
    it "returns http success" do
      get :index
      expect(response).to have_http_status(:success)
    end

    it "sets the correct page title" do
      get :index
      expect(assigns(:page_title)).to eq('Messages')
    end

    it "sets active tab to inbox by default" do
      get :index
      expect(assigns(:active_tab)).to eq('inbox')
    end

    it "sets active tab based on params" do
      get :index, params: { tab: 'chat' }
      expect(assigns(:active_tab)).to eq('chat')
    end

    it "calculates unread counts" do
      get :index
      expect(assigns(:unread_conversations_count)).to be_a(Integer)
      expect(assigns(:unread_chat_count)).to be_a(Integer)
    end

    it "renders the index template" do
      get :index
      expect(response).to render_template(:index)
    end

    it "sets the UNIFIED_MESSAGES_PAGE environment variable" do
      get :index
      expect(assigns(:js_env)[:UNIFIED_MESSAGES_PAGE]).to be true
    end

    context "when requesting JSON format" do
      it "redirects to conversations API" do
        get :index, format: :json
        expect(response).to redirect_to(api_v1_conversations_url)
      end
    end
  end
end
