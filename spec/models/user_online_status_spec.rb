require 'spec_helper'

describe UserOnlineStatus do
  let(:user) { user_model }

  describe '.update_status' do
    it 'creates a new status record for a user' do
      expect {
        UserOnlineStatus.update_status(user.id, online: true)
      }.to change(UserOnlineStatus, :count).by(1)
    end

    it 'updates existing status record for a user' do
      UserOnlineStatus.update_status(user.id, online: true)
      
      expect {
        UserOnlineStatus.update_status(user.id, online: false)
      }.not_to change(UserOnlineStatus, :count)
      
      status = UserOnlineStatus.find_by(user_id: user.id)
      expect(status.online).to be false
    end

    it 'updates last_seen_at timestamp' do
      freeze_time = Time.current
      Timecop.freeze(freeze_time) do
        UserOnlineStatus.update_status(user.id, online: true)
        status = UserOnlineStatus.find_by(user_id: user.id)
        expect(status.last_seen_at.to_i).to eq freeze_time.to_i
      end
    end
  end

  describe '.online?' do
    it 'returns false for user with no status record' do
      expect(UserOnlineStatus.online?(user.id)).to be false
    end

    it 'returns true for user marked online within threshold' do
      UserOnlineStatus.update_status(user.id, online: true)
      expect(UserOnlineStatus.online?(user.id)).to be true
    end

    it 'returns false for user marked offline' do
      UserOnlineStatus.update_status(user.id, online: false)
      expect(UserOnlineStatus.online?(user.id)).to be false
    end

    it 'returns false for user marked online but outside threshold' do
      UserOnlineStatus.update_status(user.id, online: true)
      
      Timecop.travel(UserOnlineStatus::ONLINE_THRESHOLD + 1.minute) do
        expect(UserOnlineStatus.online?(user.id)).to be false
      end
    end
  end

  describe '.mark_offline' do
    it 'marks existing user as offline' do
      UserOnlineStatus.update_status(user.id, online: true)
      UserOnlineStatus.mark_offline(user.id)
      
      status = UserOnlineStatus.find_by(user_id: user.id)
      expect(status.online).to be false
    end

    it 'does nothing for non-existent user status' do
      expect {
        UserOnlineStatus.mark_offline(user.id)
      }.not_to raise_error
    end
  end

  describe '.online_users' do
    let(:user2) { user_model }
    let(:user3) { user_model }

    before do
      UserOnlineStatus.update_status(user.id, online: true)
      UserOnlineStatus.update_status(user2.id, online: false)
      UserOnlineStatus.update_status(user3.id, online: true)
    end

    it 'returns only online users within threshold' do
      online_users = UserOnlineStatus.online_users
      expect(online_users.map(&:user_id)).to contain_exactly(user.id, user3.id)
    end

    it 'excludes users outside threshold even if marked online' do
      Timecop.travel(UserOnlineStatus::ONLINE_THRESHOLD + 1.minute) do
        online_users = UserOnlineStatus.online_users
        expect(online_users).to be_empty
      end
    end
  end

  describe '#online?' do
    it 'returns true when online and within threshold' do
      status = UserOnlineStatus.create!(user: user, online: true, last_seen_at: Time.current)
      expect(status.online?).to be true
    end

    it 'returns false when offline' do
      status = UserOnlineStatus.create!(user: user, online: false, last_seen_at: Time.current)
      expect(status.online?).to be false
    end

    it 'returns false when outside threshold' do
      status = UserOnlineStatus.create!(user: user, online: true, last_seen_at: Time.current)
      Timecop.travel(UserOnlineStatus::ONLINE_THRESHOLD + 1.minute) do
        expect(status.online?).to be false
      end
    end
  end
end
