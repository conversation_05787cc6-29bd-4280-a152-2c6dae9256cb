/*
 * Copyright (C) 2020 - present Instructure, Inc.
 *
 * This file is part of Canvas.
 *
 * Canvas is free software: you can redistribute it and/or modify it under
 * the terms of the GNU Affero General Public License as published by the Free
 * Software Foundation, version 3 of the License.
 *
 * Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
 * WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
 * A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
 * details.
 *
 * You should have received a copy of the GNU Affero General Public License along
 * with this program. If not, see <http://www.gnu.org/licenses/>.
 */

import {CanvasInboxApp} from './react/index'
import React from 'react'
import ReactDOM from 'react-dom'
import ready from '@instructure/ready'

ready(() => {
  // Check if we're in the unified messages page context
  const isUnifiedMessagesPage = !!(
    window.location.pathname === '/messages' ||
    ENV.UNIFIED_MESSAGES_PAGE
  )

  console.log('Inbox index.tsx: isUnifiedMessagesPage =', isUnifiedMessagesPage)
  console.log('Inbox index.tsx: pathname =', window.location.pathname)
  console.log('Inbox index.tsx: ENV.UNIFIED_MESSAGES_PAGE =', ENV.UNIFIED_MESSAGES_PAGE)

  let targetElement;

  if (isUnifiedMessagesPage) {
    // In unified messages page, wait for the inbox-content div to be available
    const waitForInboxContent = () => {
      targetElement = document.getElementById('inbox-content');
      if (targetElement) {
        console.log('Inbox index.tsx: Mounting to inbox-content div');
        // eslint-disable-next-line no-restricted-properties
        ReactDOM.render(<CanvasInboxApp />, targetElement);
      } else {
        console.log('Inbox index.tsx: inbox-content not ready, retrying...');
        setTimeout(waitForInboxContent, 100);
      }
    };
    waitForInboxContent();
  } else {
    // In standalone inbox page, use the content div as usual
    targetElement = document.getElementById('content');
    if (targetElement) {
      console.log('Inbox index.tsx: Mounting to content div (standalone)');
      // eslint-disable-next-line no-restricted-properties
      ReactDOM.render(<CanvasInboxApp />, targetElement);
    }
  }
})
